"use client";

import { useEffect, useState } from "react";
import { motion } from "framer-motion";
import { Loader2, Calendar, ArrowRight, BookOpen } from "lucide-react";
import {
  <PERSON>,
  <PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  CardFooter,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { GradientHeading } from "@/components/ui/gradient-heading";
import Image from "next/image";
import { cn, getAppwriteImageUrl, generateSlug } from "@/lib/utils";
import { fetchBlogs } from "@/lib/appwrite";
import { useRouter } from "next/navigation";

// Add type for blog
interface Blog {
  $id: string;
  $createdAt: string;
  title: string;
  excerpt: string;
  featuredImage?: string;
  slug?: string;
}

export default function BlogsPage() {
  const [blogs, setBlogs] = useState<Blog[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const router = useRouter();

  useEffect(() => {
    fetchBlogsFromAppwrite();
  }, []);

  const fetchBlogsFromAppwrite = async () => {
    try {
      const response = await fetchBlogs();
      console.log(response);
      setBlogs(response);
    } catch (error) {
      console.error("Error fetching blogs:", error);
    } finally {
      setIsLoading(false);
    }
  };

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <Loader2 className="w-8 h-8 animate-spin text-blue-600" />
      </div>
    );
  }

  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <section className="relative py-20 overflow-hidden">
        <div className="absolute inset-0">
          <div className="absolute -top-1/2 -right-1/2 w-[800px] h-[800px] rounded-full bg-blue-500/10 blur-3xl" />
          <div className="absolute -bottom-1/2 -left-1/2 w-[800px] h-[800px] rounded-full bg-cyan-500/10 blur-3xl" />
        </div>

        <div className="container mx-auto px-4 relative">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            className="text-center max-w-3xl mx-auto"
          >
            <GradientHeading
              size="xl"
              gradient="green"
              className="mb-6"
              as="h1"
            >
              Our Blog
            </GradientHeading>
            <p className="text-lg text-muted-foreground">
              Insights, updates, and expert perspectives from our team
            </p>
          </motion.div>
        </div>
      </section>

      {/* Blog Grid */}
      <section className="py-16">
        <div className="container mx-auto px-4">
          {blogs.length > 0 ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {blogs.map((blog, index) => (
                <motion.div
                  key={blog.$id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: index * 0.1 }}
                >
                  <Card className="group h-full flex flex-col overflow-hidden hover:shadow-xl transition-all duration-300 border-border/50 hover:border-primary/20">
                    {/* Image Container */}
                    <div className="relative w-full pt-[56.25%] overflow-hidden bg-gray-100 dark:bg-gray-800">
                      {blog.featuredImage ? (
                        <Image
                          src={getAppwriteImageUrl(blog.featuredImage)}
                          alt={blog.title}
                          fill
                          sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
                          priority={index < 3}
                          className="object-cover transform group-hover:scale-105 transition-transform duration-500"
                          onError={(e) => {
                            const target = e.target as HTMLImageElement;
                            target.style.display = "none";
                            target.parentElement?.classList.add(
                              "fallback-image"
                            );
                          }}
                        />
                      ) : (
                        <div className="absolute inset-0 flex items-center justify-center fallback-image">
                          <BookOpen className="w-12 h-12 text-muted-foreground/50" />
                        </div>
                      )}
                      {/* Gradient Overlay */}
                      <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                    </div>

                    <CardHeader>
                      <CardTitle className="line-clamp-2 text-xl group-hover:text-primary transition-colors duration-300">
                        {blog.title}
                      </CardTitle>
                    </CardHeader>

                    <CardContent className="flex-1">
                      <p className="text-muted-foreground line-clamp-3 leading-relaxed">
                        {blog.excerpt}
                      </p>
                    </CardContent>

                    <CardFooter className="flex justify-between items-center pt-6 border-t">
                      <div className="flex items-center gap-2 text-sm text-muted-foreground">
                        <Calendar className="w-4 h-4" />
                        <time dateTime={blog.$createdAt}>
                          {new Date(blog.$createdAt).toLocaleDateString(
                            "en-US",
                            {
                              year: "numeric",
                              month: "long",
                              day: "numeric",
                            }
                          )}
                        </time>
                      </div>

                      <Button
                        variant="ghost"
                        className={cn(
                          "group/button",
                          "hover:bg-primary hover:text-primary-foreground z-50"
                        )}
                        onClick={() => {
                          // Use slug if available, otherwise fall back to ID for backward compatibility
                          const blogUrl = blog.slug ? `/blogs/${blog.slug}` : `/blogs/${blog.$id}`;
                          router.push(blogUrl);
                        }}
                      >
                        Read More
                        <ArrowRight className="w-4 h-4 ml-2 group-hover/button:translate-x-1 transition-transform" />
                      </Button>
                    </CardFooter>
                  </Card>
                </motion.div>
              ))}
            </div>
          ) : (
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ duration: 0.5 }}
              className="text-center py-12"
            >
              <BookOpen className="w-16 h-16 mx-auto text-muted-foreground/50 mb-4" />
              <h3 className="text-xl font-semibold mb-2">No Articles Yet</h3>
              <p className="text-muted-foreground">
                Check back soon for new content!
              </p>
            </motion.div>
          )}
        </div>
      </section>
    </div>
  );
}
