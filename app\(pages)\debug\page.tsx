"use client";

import { useEffect, useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { PlaceholderImage } from "@/components/ui/placeholder-image";
import { 
  fetchPortfolioItems, 
  getPortfolioImageUrl, 
  testFileAccess,
  PortfolioItem 
} from "@/lib/appwrite";

export default function DebugPage() {
  const [portfolioItems, setPortfolioItems] = useState<PortfolioItem[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [testResults, setTestResults] = useState<Record<string, boolean>>({});

  const fetchData = async () => {
    setIsLoading(true);
    try {
      const items = await fetchPortfolioItems();
      setPortfolioItems(items);
      
      // Test file access for each image
      const results: Record<string, boolean> = {};
      for (const item of items) {
        if (item.image) {
          const bucketId = process.env.NEXT_PUBLIC_APPWRITE_PORTFOLIO_IMAGES_BUCKET_ID!;
          const isAccessible = await testFileAccess(bucketId, item.image);
          results[item.image] = isAccessible;
        }
      }
      setTestResults(results);
    } catch (error) {
      console.error("Error fetching data:", error);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchData();
  }, []);

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-3xl font-bold mb-8">Appwrite Debug Page</h1>
        
        {/* Environment Variables */}
        <Card className="mb-8">
          <CardHeader>
            <CardTitle>Environment Variables</CardTitle>
          </CardHeader>
          <CardContent className="space-y-2">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <strong>Project ID:</strong> 
                <Badge variant="outline" className="ml-2">
                  {process.env.NEXT_PUBLIC_APPWRITE_PROJECT_ID || "Not set"}
                </Badge>
              </div>
              <div>
                <strong>API Endpoint:</strong> 
                <Badge variant="outline" className="ml-2">
                  {process.env.NEXT_PUBLIC_APPWRITE_API_ENDPOINT || "Not set"}
                </Badge>
              </div>
              <div>
                <strong>Database ID:</strong> 
                <Badge variant="outline" className="ml-2">
                  {process.env.NEXT_PUBLIC_DATABASE_ID || "Not set"}
                </Badge>
              </div>
              <div>
                <strong>Portfolio Collection ID:</strong> 
                <Badge variant="outline" className="ml-2">
                  {process.env.NEXT_PUBLIC_APPWRITE_PORTFOLIO_COLLECTION_ID || "Not set"}
                </Badge>
              </div>
              <div>
                <strong>Portfolio Images Bucket ID:</strong> 
                <Badge variant="outline" className="ml-2">
                  {process.env.NEXT_PUBLIC_APPWRITE_PORTFOLIO_IMAGES_BUCKET_ID || "Not set"}
                </Badge>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Refresh Button */}
        <div className="mb-8">
          <Button onClick={fetchData} disabled={isLoading}>
            {isLoading ? "Loading..." : "Refresh Data"}
          </Button>
        </div>

        {/* Portfolio Items */}
        <Card>
          <CardHeader>
            <CardTitle>Portfolio Items ({portfolioItems.length})</CardTitle>
          </CardHeader>
          <CardContent>
            {portfolioItems.length === 0 ? (
              <p className="text-muted-foreground">No portfolio items found.</p>
            ) : (
              <div className="space-y-6">
                {portfolioItems.map((item) => (
                  <div key={item.$id} className="border rounded-lg p-4">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      {/* Item Info */}
                      <div>
                        <h3 className="font-semibold text-lg mb-2">{item.title}</h3>
                        <div className="space-y-2 text-sm">
                          <div>
                            <strong>ID:</strong> {item.$id}
                          </div>
                          <div>
                            <strong>Image File ID:</strong> {item.image || "No image"}
                          </div>
                          <div>
                            <strong>File Accessible:</strong> 
                            <Badge 
                              variant={testResults[item.image] ? "default" : "destructive"}
                              className="ml-2"
                            >
                              {testResults[item.image] ? "Yes" : "No"}
                            </Badge>
                          </div>
                          <div>
                            <strong>Generated URL:</strong>
                            <div className="mt-1 p-2 bg-muted rounded text-xs break-all">
                              {item.image ? getPortfolioImageUrl(item.image) : "No image"}
                            </div>
                          </div>
                        </div>
                      </div>
                      
                      {/* Image Preview */}
                      <div>
                        <div className="relative h-48 w-full border rounded">
                          {item.image ? (
                            <PlaceholderImage
                              src={getPortfolioImageUrl(item.image)}
                              alt={item.title}
                              fill
                              className="object-cover rounded"
                              timeout={3000} // 3 second timeout for debug
                            />
                          ) : (
                            <div className="flex items-center justify-center h-full text-muted-foreground">
                              No image
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
