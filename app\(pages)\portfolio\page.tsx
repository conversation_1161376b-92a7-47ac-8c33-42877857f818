"use client";

import { useEffect, useState } from "react";
import { motion } from "framer-motion";
import Link from "next/link";
import { ExternalLink, Loader2 } from "lucide-react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Meteors } from "@/components/ui/meteors";
import { PlaceholderImage } from "@/components/ui/placeholder-image";
import { cn } from "@/lib/utils";
import { GradientHeading } from "@/components/ui/gradient-heading";
import { PortfolioItem, fetchPortfolioItems, getPortfolioImageUrl } from "@/lib/appwrite";

// Gradient colors for different categories using Flerid's color palette
const gradientMap: Record<string, string> = {
  "Web Development": "from-navy to-teal",
  "Mobile App": "from-teal to-navy",
  "UI/UX Design": "from-navy to-warm-gray",
  "E-commerce": "from-teal to-warm-gray",
  "Default": "from-navy to-teal",
};

const cardVariants = {
  hidden: { opacity: 0, y: 20 },
  visible: (i: number) => ({
    opacity: 1,
    y: 0,
    transition: {
      delay: i * 0.1,
      duration: 0.5,
      ease: "easeOut",
    },
  }),
};

export default function PortfolioPage() {
  const [portfolioItems, setPortfolioItems] = useState<PortfolioItem[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const fetchPortfolio = async () => {
      try {
        // Fetch portfolio items - sorted by creation date (newest first)
        const items = await fetchPortfolioItems();
        setPortfolioItems(items);
      } catch (error) {
        console.error("Error fetching portfolio items:", error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchPortfolio();
  }, []);

  // Function to determine the grid class based on index
  const getGridClass = (index: number) => {
    // Make cards smaller and more consistent
    if (index % 4 === 0) {
      return "md:col-span-1";
    } else if (index % 3 === 0) {
      return "md:col-span-1";
    } else {
      return "md:col-span-1";
    }
  };

  return (
    <div className="min-h-screen bg-background text-foreground">
      {/* Hero Section */}
      <section className="relative py-24 overflow-hidden">


        <div className="container mx-auto px-4 relative z-10">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, ease: "easeOut" }}
            className="text-center max-w-4xl mx-auto"
          >
            <motion.div
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.6, delay: 0.2 }}
            >
              <GradientHeading
                size="xl"
                gradient="blue"
                className="mb-8 leading-tight"
                as="h1"
              >
                Our Portfolio
              </GradientHeading>
            </motion.div>

            <motion.p
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.4 }}
              className="text-xl text-muted-foreground mb-12 leading-relaxed max-w-3xl mx-auto"
            >
              Explore some of our recent projects and see how we've helped businesses achieve their digital goals through innovative web development, mobile applications, and digital solutions.
            </motion.p>


          </motion.div>
        </div>
      </section>

      {/* Portfolio Grid */}
      <section className="py-20 relative">

        <div className="container mx-auto px-4 relative z-10">
          {/* Section header */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            className="text-center mb-16"
          >
            <h2 className="text-3xl font-bold text-foreground mb-4">Featured Projects</h2>
            <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
              Discover our latest work and see how we bring innovative ideas to life
            </p>
          </motion.div>

          {isLoading ? (
            <div className="flex justify-center items-center py-20">
              <div className="flex flex-col items-center gap-4">
                <Loader2 className="w-12 h-12 animate-spin text-primary" />
                <p className="text-muted-foreground font-medium">Loading our amazing projects...</p>
              </div>
            </div>
          ) : portfolioItems.length === 0 ? (
            <div className="text-center py-20">
              <div className="bg-card border rounded-xl p-12 max-w-md mx-auto shadow-lg">
                <div className="w-16 h-16 bg-gradient-to-br from-blue-500 to-cyan-500 rounded-full flex items-center justify-center mx-auto mb-6">
                  <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
                  </svg>
                </div>
                <h3 className="text-xl font-semibold text-foreground mb-2">Coming Soon!</h3>
                <p className="text-muted-foreground">We're working on some amazing projects. Check back soon to see our latest work!</p>
              </div>
            </div>
          ) : (
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-8 max-w-7xl mx-auto">
              {portfolioItems.map((project, index) => (
                <motion.div
                  key={project.$id}
                  custom={index}
                  initial="hidden"
                  whileInView="visible"
                  viewport={{ once: true }}
                  variants={cardVariants}
                  className={getGridClass(index)}
                >
                  <Card
                    className={cn(
                      `group relative overflow-hidden border hover:border-primary/50 transition-all duration-500 h-full shadow-lg hover:shadow-xl hover:-translate-y-2 transform bg-card`
                    )}
                  >
                    {/* Project Image */}
                    <div className="relative h-64 w-full overflow-hidden rounded-t-lg">
                      <PlaceholderImage
                        src={project.image ? getPortfolioImageUrl(project.image) : "/placeholder-project.svg"}
                        alt={project.title}
                        fill
                        className="object-cover object-center transition-all duration-700 group-hover:scale-110 group-hover:brightness-110"
                      />
                      {/* Enhanced gradient overlay */}
                      <div
                        className={cn(
                          "absolute inset-0 opacity-30 group-hover:opacity-20 transition-opacity duration-500 bg-gradient-to-t from-black/60 via-transparent to-transparent"
                        )}
                      />
                      <div
                        className={cn(
                          "absolute inset-0 opacity-20 group-hover:opacity-30 transition-opacity duration-500 bg-gradient-to-br",
                          gradientMap[project.category] || gradientMap["Default"]
                        )}
                      />

                      {/* Category badge */}
                      <div className="absolute top-4 left-4">
                        <Badge
                          variant="secondary"
                          className="bg-background/95 backdrop-blur-sm border shadow-lg font-medium"
                        >
                          {project.category}
                        </Badge>
                      </div>

                      {/* Hover overlay with view button */}
                      <div className="absolute inset-0 bg-gradient-to-t from-blue-900/80 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-all duration-500 flex items-end justify-center pb-6">
                        <Button
                          size="sm"
                          className="bg-background/20 hover:bg-background/30 text-white border border-white/30 backdrop-blur-sm transform translate-y-4 group-hover:translate-y-0 transition-transform duration-500"
                        >
                          View Project
                          <ExternalLink className="w-4 h-4 ml-2" />
                        </Button>
                      </div>
                    </div>

                    <CardHeader className="p-6 pb-3">
                      <CardTitle className="text-xl font-bold mb-2 text-foreground group-hover:text-blue-600 dark:group-hover:text-cyan-400 transition-colors duration-300">
                        {project.title}
                      </CardTitle>
                      <CardDescription className="text-sm text-muted-foreground line-clamp-3 leading-relaxed">
                        {project.description}
                      </CardDescription>
                    </CardHeader>

                    <CardContent className="z-10 p-6 pt-0 space-y-4">
                      {/* Features */}
                      {project.features && project.features.length > 0 && (
                        <div>
                          <h4 className="text-sm font-semibold text-foreground mb-3 flex items-center gap-2">
                            <div className="w-2 h-2 bg-gradient-to-r from-blue-500 to-cyan-500 rounded-full"></div>
                            Key Features
                          </h4>
                          <div className="flex flex-wrap gap-2">
                            {project.features.slice(0, 3).map((feature, idx) => {
                              // Enhanced color scheme for features
                              const featureColors = [
                                "bg-gradient-to-r from-blue-500/20 to-cyan-500/20 text-blue-700 dark:text-blue-300 border-blue-300/50",
                                "bg-gradient-to-r from-indigo-500/20 to-purple-500/20 text-indigo-700 dark:text-indigo-300 border-indigo-300/50",
                                "bg-gradient-to-r from-emerald-500/20 to-teal-500/20 text-emerald-700 dark:text-emerald-300 border-emerald-300/50",
                                "bg-gradient-to-r from-violet-500/20 to-purple-500/20 text-violet-700 dark:text-violet-300 border-violet-300/50",
                                "bg-gradient-to-r from-rose-500/20 to-pink-500/20 text-rose-700 dark:text-rose-300 border-rose-300/50",
                              ];
                              const colorIndex = Math.abs(feature.title.split('').reduce((acc, char) => acc + char.charCodeAt(0), 0)) % featureColors.length;

                              return (
                                <div
                                  key={idx}
                                  className={`${featureColors[colorIndex]} px-3 py-1.5 rounded-full text-xs font-medium border backdrop-blur-sm`}
                                  title={feature.description}
                                >
                                  {feature.title}
                                </div>
                              );
                            })}
                            {project.features.length > 3 && (
                              <div className="px-3 py-1.5 rounded-full text-xs font-medium bg-gray-100 dark:bg-gray-800 text-gray-600 dark:text-gray-400 border border-gray-300/50">
                                +{project.features.length - 3} more
                              </div>
                            )}
                          </div>
                        </div>
                      )}

                      {/* Technologies */}
                      <div className="mb-6">
                        <h4 className="text-sm font-semibold text-foreground mb-3 flex items-center gap-2">
                          <div className="w-2 h-2 bg-gradient-to-r from-indigo-500 to-purple-500 rounded-full"></div>
                          Technologies
                        </h4>
                        <div className="flex flex-wrap gap-2">
                          {project.technologies.slice(0, 4).map((tech) => {
                            // Enhanced color scheme for technologies
                            const colors = [
                              "bg-gradient-to-r from-gray-500/20 to-gray-600/20 text-gray-700 dark:text-gray-300 border-gray-400/50",
                              "bg-gradient-to-r from-blue-500/20 to-indigo-500/20 text-blue-700 dark:text-blue-300 border-blue-400/50",
                              "bg-gradient-to-r from-green-500/20 to-emerald-500/20 text-green-700 dark:text-green-300 border-green-400/50",
                              "bg-gradient-to-r from-purple-500/20 to-violet-500/20 text-purple-700 dark:text-purple-300 border-purple-400/50",
                              "bg-gradient-to-r from-orange-500/20 to-red-500/20 text-orange-700 dark:text-orange-300 border-orange-400/50",
                              "bg-gradient-to-r from-pink-500/20 to-rose-500/20 text-pink-700 dark:text-pink-300 border-pink-400/50",
                            ];
                            const colorIndex = Math.abs(tech.split('').reduce((acc, char) => acc + char.charCodeAt(0), 0)) % colors.length;

                            return (
                              <Badge
                                key={tech}
                                variant="secondary"
                                className={`${colors[colorIndex]} text-xs px-3 py-1 rounded-full border backdrop-blur-sm font-medium`}
                              >
                                {tech}
                              </Badge>
                            );
                          })}
                          {project.technologies.length > 4 && (
                            <Badge
                              variant="secondary"
                              className="bg-gray-100 dark:bg-gray-800 text-gray-600 dark:text-gray-400 text-xs px-3 py-1 rounded-full border border-gray-300/50"
                            >
                              +{project.technologies.length - 4}
                            </Badge>
                          )}
                        </div>
                      </div>

                      {/* Enhanced Action Buttons */}
                      <div className="flex gap-3 pt-2">
                        {project.projectUrl ? (
                          <Button
                            variant="default"
                            className="group/button bg-gradient-to-r from-blue-600 to-cyan-600 hover:from-blue-700 hover:to-cyan-700 text-white flex-1 shadow-lg hover:shadow-xl transition-all duration-300"
                            asChild
                          >
                            <Link href={project.projectUrl}>
                              View Live
                              <ExternalLink className="ml-2 h-4 w-4 transition-transform group-hover/button:translate-x-1 group-hover/button:-translate-y-0.5" />
                            </Link>
                          </Button>
                        ) : (
                          <Button
                            variant="outline"
                            className="group/button flex-1 cursor-not-allowed opacity-60"
                            disabled
                          >
                            Coming Soon
                          </Button>
                        )}

                        <Button
                          variant="ghost"
                          size="icon"
                          className="text-primary hover:bg-muted transition-colors duration-300"
                          title="View Details"
                        >
                          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                          </svg>
                        </Button>
                      </div>
                    </CardContent>

                    <Meteors number={10} />
                  </Card>
                </motion.div>
              ))}
            </div>
          )}
        </div>
      </section>
    </div>
  );
}