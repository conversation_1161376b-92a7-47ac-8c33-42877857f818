"use client";

import { motion } from "framer-motion";
import { Check, Sparkles } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { GradientHeading } from "@/components/ui/gradient-heading";
import { cn } from "@/lib/utils";

const plans = [
  {
    name: "Basic",
    price: "₹5,999",
    description: "Perfect for small businesses just getting started",
    features: [
      "5 Pages Website",
      "Mobile Responsive Design", 
      "Contact Form Integration",
      "Basic SEO Setup",
      "Social Media Integration",
      "1 Month Free Support",
      "2 Revisions",
      "Basic Analytics"
    ],
    gradient: "from-blue-500 to-cyan-500",
    href: "/#booking"
  },
  {
    name: "Professional",
    price: "₹12,999",
    description: "Ideal for growing businesses needing more features",
    features: [
      "10 Pages Website",
      "Mobile Responsive Design",
      "Advanced Contact Forms",
      "Complete SEO Optimization",
      "Social Media Integration",
      "3 Months Free Support",
      "5 Revisions",
      "Advanced Analytics",
      "Custom Animations",
      "Blog Section",
      "Newsletter Integration",
      "Priority Support"
    ],
    isPopular: true,
    gradient: "from-violet-500 to-purple-500",
    href: "/#booking"
  },
  {
    name: "Enterprise",
    price: "Custom",
    description: "For large businesses with specific requirements",
    features: [
      "Unlimited Pages",
      "Custom Web Application",
      "Advanced Features",
      "Complete SEO Suite",
      "Social Media Management",
      "12 Months Support",
      "Unlimited Revisions",
      "Premium Analytics",
      "Custom Animations",
      "Blog & News Section",
      "Email Marketing",
      "24/7 Priority Support",
      "Custom Integrations",
      "Dedicated Manager"
    ],
    gradient: "from-red-500 to-rose-500",
    href: "/#booking"
  }
];

export default function PricingPage() {
  return (
    <div className="min-h-screen">
      {/* Header Section */}
      <section className="py-20 relative overflow-hidden">
        <div className="absolute inset-0 overflow-hidden rounded-lg">
          <div className="absolute -top-1/2 -right-1/2 w-[800px] h-[800px] rounded-full bg-blue-500/10 blur-3xl" />
          <div className="absolute -bottom-1/2 -left-1/2 w-[800px] h-[800px] rounded-full bg-purple-500/10 blur-3xl" />
        </div>
        
        <div className="container mx-auto px-4 relative ">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            className="text-center max-w-3xl mx-auto"
          >
            <GradientHeading
              size="xl"
              gradient="blue"
              className="mb-6"
              as="h1"
            >
              Simple, Transparent Pricing
            </GradientHeading>
            <p className="text-lg text-muted-foreground">
              Choose the perfect plan for your business needs. No hidden fees, just straightforward pricing.
            </p>
          </motion.div>
        </div>
      </section>

      {/* Pricing Cards */}
      <section className="py-16 relative">
        <div className="container mx-auto px-4">
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {plans.map((plan, index) => (
              <motion.div
                key={plan.name}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
                className="relative h-full"
              >
                {/* Popular Badge */}
                {plan.isPopular && (
                  <div className="absolute -top-1 inset-x-0 flex justify-center z-10">
                    <Badge
                      variant="default"
                      className={cn(
                        "bg-gradient-to-r from-violet-500 to-purple-500",
                        "border-0 text-white font-semibold",
                        "px-6 py-1.5 rounded-full shadow-lg",
                        "flex items-center gap-2",
                        "absolute"
                      )}
                    >
                      <Sparkles className="w-4 h-4" />
                      Most Popular
                    </Badge>
                  </div>
                )}

                {/* Card */}
                <div className={cn(
                  "h-full rounded-2xl",
                  "bg-card border border-border/50",
                  "transition-all duration-300",
                  "hover:shadow-lg hover:shadow-purple-500/5",
                  "relative overflow-hidden group",
                  "flex flex-col",
                  plan.isPopular && "ring-2 ring-purple-500/20",
                  "mt-4"
                )}>
                  {/* Background Gradient */}
                  <div className={cn(
                    "absolute inset-0 opacity-0 bg-gradient-to-br transition-opacity duration-300",
                    plan.gradient,
                    "group-hover:opacity-[0.03]"
                  )} />

                  {/* Content */}
                  <div className="relative p-8 flex-1 flex flex-col">
                    {/* Header */}
                    <div className="mb-8">
                      <h3 className="text-2xl font-bold mb-2">{plan.name}</h3>
                      <div className="flex items-baseline gap-1 mb-2">
                        <span className="text-4xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-blue-600 to-cyan-600">
                          {plan.price}
                        </span>
                        {plan.price !== "Custom" && <span className="text-muted-foreground">/project</span>}
                      </div>
                      <p className="text-sm text-muted-foreground">
                        {plan.description}
                      </p>
                    </div>

                    {/* Features */}
                    <div className="flex-1">
                      <ul className="space-y-3 text-sm mb-8">
                        {plan.features.map((feature) => (
                          <li key={feature} className="flex items-start gap-2">
                            <Check className="w-5 h-5 text-green-500 shrink-0" />
                            <span>{feature}</span>
                          </li>
                        ))}
                      </ul>
                    </div>

                    {/* CTA Button */}
                    <Button
                      className={cn(
                        "w-full",
                        "bg-gradient-to-r",
                        plan.gradient,
                        "text-white border-0",
                        "hover:opacity-90",
                        "shadow-lg font-medium"
                      )}
                      size="lg"
                      asChild
                      style={{
                        textShadow: '0 1px 2px rgba(0, 0, 0, 0.7)'
                      }}
                      onClick={() => {
                        // Add smooth scrolling behavior
                        document.getElementById('booking')?.scrollIntoView({
                          behavior: 'smooth'
                        });
                      }}
                    >
                      <a
                        href={plan.href}
                        className="service-button-text"
                      >
                        Get Started
                      </a>
                    </Button>
                  </div>
                </div>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* FAQ Section */}
      <section className="py-16 bg-muted/50">
        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.5 }}
            className="max-w-3xl mx-auto text-center"
          >
            <h2 className="text-3xl font-bold mb-4">Frequently Asked Questions</h2>
            <p className="text-muted-foreground mb-12">
              Have questions? We&apos;re here to help.
            </p>

            <div className="grid gap-8 text-left">
              {[
                {
                  q: "What's included in the project cost?",
                  a: "Our pricing includes everything from initial consultation, design, development, testing, and deployment. We also provide post-launch support to ensure everything runs smoothly."
                },
                {
                  q: "How long does a typical project take?",
                  a: "Project timelines vary based on complexity. Basic websites typically take 2-3 weeks, while more complex projects may take 6-8 weeks or more. We'll provide a detailed timeline during our consultation."
                },
                {
                  q: "Do you offer custom solutions?",
                  a: "Yes! Our Enterprise plan is fully customizable to meet your specific requirements. Contact us to discuss your needs and get a custom quote."
                }
              ].map((faq, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  viewport={{ once: true }}
                  transition={{ duration: 0.5, delay: index * 0.1 }}
                  className="bg-card border border-border/50 rounded-lg p-6"
                >
                  <h3 className="font-semibold mb-2">{faq.q}</h3>
                  <p className="text-muted-foreground">{faq.a}</p>
                </motion.div>
              ))}
            </div>
          </motion.div>
        </div>
      </section>
    </div>
  );
}