"use client";

import { useParams } from "next/navigation";
import Image from "next/image";
import { motion } from "framer-motion";
import {
  Code2,
  <PERSON><PERSON>,
  <PERSON>,
  <PERSON>Chart,
  Share,
  Check,
  ArrowRight,
  ChevronRight,
  Home,
} from "lucide-react";
import { servicesData } from "@/lib/data";
import { cn } from "@/lib/utils";
import { Button } from "@/components/ui/button";
import { GradientButton } from "@/components/ui/gradient-button";
import { GradientHeading } from "@/components/ui/gradient-heading";
import { Section } from "@/components/ui/section";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { ServiceBreadcrumb } from "@/components/ui/service-breadcrumb";
import { ServiceIcon } from "@/components/ui/service-icon";
import { RelatedServices } from "@/components/related-services";
import Link from "next/link";
import React from "react";



export default function ServicePage() {
  const { id } = useParams();
  const service = servicesData.find((s) => s.id === id);

  if (!service) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-4xl font-bold mb-4">Service not found</h1>
          <p className="text-muted-foreground mb-8">
            The service you&apos;re looking for doesn&apos;t exist.
          </p>
          <Button asChild>
            <Link href="/services">Back to Services</Link>
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen flex flex-col">
      {/* Breadcrumb Navigation */}
      <ServiceBreadcrumb serviceTitle={service.title} />

      {/* Hero Section */}
      <section className="relative h-[60vh] min-h-[500px]">
        <div className="absolute inset-0">
          <Image
            src={service.heroImage}
            alt={service.title}
            fill
            className="object-cover"
            priority
          />
          <div
            className={cn(
              "absolute inset-0 opacity-90",
              "bg-gradient-to-br",
              service.color
            )}
          />
          <div className="absolute inset-0 bg-black/50" />
        </div>

        <div className="relative h-full container mx-auto px-4 flex items-center">
          <div className="max-w-3xl">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
              className={cn(
                "inline-flex items-center justify-center w-20 h-20 rounded-2xl mb-6",
                "bg-white/10 backdrop-blur-sm",
                "text-white"
              )}
            >
              <ServiceIcon iconName={service.icon as string} />
            </motion.div>
            <motion.h1
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.1 }}
              className="text-4xl md:text-5xl font-bold mb-4 text-white"
            >
              {service.title}
            </motion.h1>
            <motion.p
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.2 }}
              className="text-xl text-white/90"
            >
              {service.description}
            </motion.p>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <Section background="gradient" padding="xl">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          className="text-center mb-12"
        >
          <GradientHeading
            size="lg"
            gradient="primary"
            className="mb-4"
            as="h2"
          >
            Key Features
          </GradientHeading>
          <p className="text-muted-foreground text-lg">
            Everything you need for professional {service.title.toLowerCase()}
          </p>
        </motion.div>

          <div className="grid md:grid-cols-2 gap-6">
            {service.features.map((feature, index) => (
              <motion.div
                key={feature.title}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ delay: index * 0.1 }}
              >
                <Card className="h-full hover:shadow-lg transition-shadow border-border/50 hover:border-primary/20">
                  <CardHeader>
                    <CardTitle className="text-xl">{feature.title}</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <p className="text-muted-foreground">
                      {feature.description}
                    </p>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>
      </Section>

      {/* Process Section */}
      <section className="py-16 bg-muted/50">
        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            className="text-center mb-12"
          >
            <h2 className="text-3xl font-bold mb-4">Our Process</h2>
            <p className="text-muted-foreground">
              How we deliver exceptional {service.title.toLowerCase()} solutions
            </p>
          </motion.div>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
            {service.process.map((step, index) => (
              <motion.div
                key={step.title}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ delay: index * 0.1 }}
              >
                <Card className="relative h-full hover:shadow-lg transition-shadow overflow-hidden group border-border/50 hover:border-primary/20 isolate">
                  <div
                    className={cn(
                      "absolute inset-0 opacity-0 transition-opacity duration-300 pointer-events-none",
                      "bg-gradient-to-br",
                      service.color,
                      "group-hover:opacity-[0.03]"
                    )}
                    style={{ zIndex: 1 }}
                  />
                  <CardHeader className="relative" style={{ zIndex: 2 }}>
                    <div className="flex items-center gap-4">
                      <div
                        className={cn(
                          "w-10 h-10 rounded-full flex items-center justify-center text-white",
                          "bg-gradient-to-br",
                          service.color
                        )}
                      >
                        {index + 1}
                      </div>
                      <CardTitle>{step.title}</CardTitle>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <p className="text-muted-foreground">{step.description}</p>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Technologies & Benefits */}
      <section className="py-16 bg-gradient-to-b from-muted/50 to-background">
        <div className="container mx-auto px-4">
          <div className="grid md:grid-cols-2 gap-12">
            {/* Technologies */}
            <motion.div
              initial={{ opacity: 0, x: -20 }}
              whileInView={{ opacity: 1, x: 0 }}
              viewport={{ once: true }}
            >
              <Card className="h-full">
                <CardHeader>
                  <CardTitle className="text-2xl font-bold">
                    Technologies We Use
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-2 gap-4">
                    {service.technologies.map((tech) => (
                      <div
                        key={tech}
                        className={cn(
                          "flex items-center gap-2 p-3 rounded-lg",
                          "bg-muted/50 hover:bg-muted",
                          "transition-colors duration-200"
                        )}
                      >
                        <div
                          className={cn(
                            "w-8 h-8 rounded-lg flex items-center justify-center",
                            "bg-gradient-to-br",
                            service.color,
                            "bg-opacity-10"
                          )}
                        >
                          <Check className="w-4 h-4 text-white" />
                        </div>
                        <span className="font-medium">{tech}</span>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </motion.div>

            {/* Benefits */}
            <motion.div
              initial={{ opacity: 0, x: 20 }}
              whileInView={{ opacity: 1, x: 0 }}
              viewport={{ once: true }}
            >
              <Card className="h-full">
                <CardHeader>
                  <CardTitle className="text-2xl font-bold">
                    Key Benefits
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid gap-4">
                    {service.benefits.map((benefit, index) => (
                      <div
                        key={benefit}
                        className={cn(
                          "flex items-center gap-4 p-3 rounded-lg",
                          "bg-muted/50 hover:bg-muted",
                          "transition-colors duration-200"
                        )}
                      >
                        <div
                          className={cn(
                            "w-8 h-8 rounded-lg flex items-center justify-center",
                            "bg-gradient-to-br",
                            service.color,
                            "bg-opacity-10"
                          )}
                        >
                          <span className="font-semibold text-sm">
                            {(index + 1).toString().padStart(2, "0")}
                          </span>
                        </div>
                        <span className="font-medium">{benefit}</span>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <Section background="muted" padding="xl">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          className="max-w-2xl mx-auto text-center"
        >
          <GradientHeading
            size="lg"
            gradient="primary"
            className="mb-4"
            as="h2"
          >
            Ready to Get Started?
          </GradientHeading>
          <p className="text-muted-foreground mb-8 text-lg">
            Let&apos;s discuss how our {service.title.toLowerCase()} services can
            help your business grow.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <GradientButton
              size="xl"
              gradient="primary"
              asChild
            >
              <Link
                href="/contact"
                className="service-button-text flex items-center"
              >
                Contact Us
                <ArrowRight className="ml-2 w-4 h-4" />
              </Link>
            </GradientButton>
            <Button asChild variant="outline" size="lg" className="h-12 px-8">
              <Link href="/services">
                View All Services
              </Link>
            </Button>
          </div>
        </motion.div>
      </Section>

      {/* Related Services */}
      <RelatedServices currentServiceId={service.id} />
    </div>
  );
}
