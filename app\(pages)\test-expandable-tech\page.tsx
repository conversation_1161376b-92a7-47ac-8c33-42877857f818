"use client";

import { motion } from "framer-motion";
import { ServiceCard } from "@/components/ui/service-card";
import { servicesData } from "@/lib/data";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";

const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1
    }
  }
};

export default function TestExpandableTechPage() {
  // Get services with different technology counts for testing
  const testServices = servicesData.filter(service => 
    service.technologies.length > 4 // Only services with enough techs to show "+X more"
  );

  return (
    <div className="min-h-screen py-8">
      <div className="container mx-auto px-4">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="text-center mb-8"
        >
          <h1 className="text-4xl font-bold mb-4">Expandable Technology Badges Test</h1>
          <p className="text-muted-foreground mb-6">
            Testing the expand/collapse functionality for technology badges in ServiceCard components
          </p>
          
          <div className="flex flex-wrap justify-center gap-4 mb-8">
            <Badge variant="secondary" className="px-4 py-2">
              ✅ Click "many more" to expand
            </Badge>
            <Badge variant="outline" className="px-4 py-2">
              ✅ Badge disappears after expansion
            </Badge>
            <Badge variant="outline" className="px-4 py-2">
              ✅ Smooth animations
            </Badge>
          </div>
        </motion.div>

        {/* Regular Variant Test */}
        <section className="mb-12">
          <h2 className="text-2xl font-bold mb-6">Regular ServiceCard Variant</h2>
          <p className="text-muted-foreground mb-6">
            Regular cards show up to 4 technologies initially, then "many more" for additional ones.
          </p>
          
          <motion.div
            variants={containerVariants}
            initial="hidden"
            animate="visible"
            className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"
          >
            {testServices.slice(0, 3).map((service, index) => (
              <ServiceCard
                key={`regular-${service.id}`}
                service={service}
                index={index}
                variant="default"
              />
            ))}
          </motion.div>
        </section>

        {/* Compact Variant Test */}
        <section className="mb-12">
          <h2 className="text-2xl font-bold mb-6">Compact ServiceCard Variant</h2>
          <p className="text-muted-foreground mb-6">
            Compact cards show up to 3 technologies initially, then "many more" for additional ones.
          </p>
          
          <motion.div
            variants={containerVariants}
            initial="hidden"
            animate="visible"
            className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"
          >
            {testServices.slice(0, 3).map((service, index) => (
              <ServiceCard
                key={`compact-${service.id}`}
                service={service}
                index={index}
                variant="compact"
              />
            ))}
          </motion.div>
        </section>

        {/* Technology Count Analysis */}
        <section className="mb-8">
          <h2 className="text-2xl font-bold mb-6">Technology Count Analysis</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {servicesData.map((service) => (
              <Card key={service.id} className="p-4">
                <CardHeader className="pb-2">
                  <CardTitle className="text-lg">{service.title}</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span>Total Technologies:</span>
                      <Badge variant="secondary">{service.technologies.length}</Badge>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span>Regular variant shows:</span>
                      <span>{Math.min(4, service.technologies.length)} initially</span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span>Compact variant shows:</span>
                      <span>{Math.min(3, service.technologies.length)} initially</span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span>Has "many more" badge:</span>
                      <Badge variant={service.technologies.length > 4 ? "default" : "outline"}>
                        {service.technologies.length > 4 ? "Yes" : "No"}
                      </Badge>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </section>

        {/* Layout Stability Test */}
        <section className="mb-8">
          <h2 className="text-2xl font-bold mb-6">Layout Stability Test</h2>
          <p className="text-muted-foreground mb-6">
            Test that expanding/collapsing doesn't affect neighboring cards or break the grid layout.
          </p>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {servicesData.slice(0, 4).map((service, index) => (
              <ServiceCard
                key={`stability-${service.id}`}
                service={service}
                index={index}
                variant="compact"
              />
            ))}
          </div>

          <div className="mt-4 p-4 bg-muted/50 rounded-lg">
            <h4 className="font-semibold mb-2">Layout Test Instructions:</h4>
            <ul className="text-sm space-y-1">
              <li>• Click "many more" on one card and verify badge disappears</li>
              <li>• Expand multiple cards simultaneously</li>
              <li>• Check that grid layout doesn't break</li>
              <li>• Verify smooth animations without layout jumps</li>
            </ul>
          </div>
        </section>

        {/* Test Instructions */}
        <Card>
          <CardHeader>
            <CardTitle>Complete Test Instructions</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid md:grid-cols-2 gap-6">
              <div>
                <h4 className="font-semibold mb-3">Basic Functionality</h4>
                <ol className="list-decimal list-inside space-y-2 text-sm">
                  <li>Look for service cards with "many more" badges</li>
                  <li>Click "many more" to expand technologies</li>
                  <li>Verify badge disappears after expansion</li>
                  <li>Check that all technologies are now visible</li>
                  <li>Test both regular and compact variants</li>
                </ol>
              </div>
              <div>
                <h4 className="font-semibold mb-3">Advanced Testing</h4>
                <ol className="list-decimal list-inside space-y-2 text-sm">
                  <li>Verify animations are smooth (0.2s duration)</li>
                  <li>Check layout stability with multiple expansions</li>
                  <li>Ensure each card's state is independent</li>
                  <li>Test on different screen sizes</li>
                  <li>Verify "many more" badge disappears permanently</li>
                </ol>
              </div>
            </div>

            <div className="mt-6 p-4 bg-blue-50 dark:bg-blue-950/20 rounded-lg">
              <h4 className="font-semibold mb-2 flex items-center gap-2">
                <Badge variant="secondary">Expected Behavior</Badge>
              </h4>
              <ul className="text-sm space-y-1">
                <li>• Regular cards: Show 4 technologies initially, "many more" for remaining 4-6</li>
                <li>• Compact cards: Show 3 technologies initially, "many more" for remaining 5-7</li>
                <li>• Smooth scale animation (not height-based to prevent layout shifts)</li>
                <li>• "many more" badge disappears after clicking (no collapse option)</li>
                <li>• Each card maintains independent expansion state</li>
              </ul>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
