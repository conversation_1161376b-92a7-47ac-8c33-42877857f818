"use client";

import { useState, useEffect } from "react";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { CheckCircle, XCircle, RefreshCw } from "lucide-react";

export default function TestFaviconPage() {
  const [faviconTests, setFaviconTests] = useState<Record<string, boolean | null>>({});
  const [isLoading, setIsLoading] = useState(false);

  const faviconPaths = [
    '/favicon.ico',
    '/favicons/favicon.ico',
    '/favicons/favicon-16x16.png',
    '/favicons/favicon-32x32.png',
    '/favicons/apple-touch-icon.png',
    '/favicons/android-chrome-192x192.png',
    '/favicons/android-chrome-512x512.png',
    '/manifest.json'
  ];

  const testFavicon = async (path: string) => {
    try {
      const response = await fetch(path, { method: 'HEAD' });
      return response.ok;
    } catch (error) {
      console.error(`Error testing favicon at ${path}:`, error);
      return false;
    }
  };

  const runFaviconTests = async () => {
    setIsLoading(true);
    const results: Record<string, boolean> = {};
    
    for (const path of faviconPaths) {
      const isAccessible = await testFavicon(path);
      results[path] = isAccessible;
    }
    
    setFaviconTests(results);
    setIsLoading(false);
  };

  useEffect(() => {
    runFaviconTests();
  }, []);

  return (
    <div className="min-h-screen py-8">
      <div className="container mx-auto px-4 max-w-4xl">
        <div className="text-center mb-8">
          <h1 className="text-4xl font-bold mb-4">Favicon Test & Debug</h1>
          <p className="text-muted-foreground mb-6">
            Testing favicon accessibility and configuration to resolve 500 errors
          </p>
          
          <div className="flex flex-wrap justify-center gap-4 mb-8">
            <Badge variant="secondary" className="px-4 py-2">
              Next.js App Router
            </Badge>
            <Badge variant="outline" className="px-4 py-2">
              Favicon Configuration
            </Badge>
            <Badge variant="outline" className="px-4 py-2">
              Error Debugging
            </Badge>
          </div>

          <Button 
            onClick={runFaviconTests} 
            disabled={isLoading}
            className="mb-8"
          >
            {isLoading ? (
              <>
                <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
                Testing...
              </>
            ) : (
              <>
                Re-run Tests
                <RefreshCw className="w-4 h-4 ml-2" />
              </>
            )}
          </Button>
        </div>

        {/* Favicon Test Results */}
        <div className="grid gap-6 mb-8">
          <Card>
            <CardHeader>
              <CardTitle>Favicon Accessibility Tests</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {faviconPaths.map((path) => (
                  <div key={path} className="flex items-center justify-between p-4 border rounded-lg">
                    <div>
                      <code className="text-sm bg-muted px-2 py-1 rounded">{path}</code>
                    </div>
                    <div className="flex items-center gap-2">
                      {faviconTests[path] === true && (
                        <>
                          <CheckCircle className="w-5 h-5 text-green-500" />
                          <Badge variant="default">Accessible</Badge>
                        </>
                      )}
                      {faviconTests[path] === false && (
                        <>
                          <XCircle className="w-5 h-5 text-red-500" />
                          <Badge variant="destructive">Not Found</Badge>
                        </>
                      )}
                      {faviconTests[path] === null && (
                        <Badge variant="outline">Testing...</Badge>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Current Configuration */}
        <Card className="mb-8">
          <CardHeader>
            <CardTitle>Current Favicon Configuration</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div>
                <h4 className="font-semibold mb-2">Favicons Directory Method</h4>
                <p className="text-sm text-muted-foreground mb-2">
                  Using dedicated favicons directory with complete icon set
                </p>
                <div className="space-y-2">
                  <code className="text-sm bg-muted px-2 py-1 rounded block">
                    app/favicons/favicon.ico ✅ (Main favicon)
                  </code>
                  <code className="text-sm bg-muted px-2 py-1 rounded block">
                    app/favicons/*.png ✅ (Multiple sizes)
                  </code>
                  <code className="text-sm bg-muted px-2 py-1 rounded block">
                    public/manifest.json ✅ (Web app manifest)
                  </code>
                </div>
              </div>
              
              <div>
                <h4 className="font-semibold mb-2">Legacy Public Method</h4>
                <p className="text-sm text-muted-foreground mb-2">
                  Traditional method using public directory
                </p>
                <code className="text-sm bg-muted px-2 py-1 rounded block">
                  public/favicon.ico ✅ (File exists)
                </code>
              </div>
              
              <div>
                <h4 className="font-semibold mb-2">Metadata Configuration</h4>
                <p className="text-sm text-muted-foreground mb-2">
                  Current layout.tsx metadata setup
                </p>
                <pre className="text-sm bg-muted p-3 rounded overflow-x-auto">
{`export const metadata: Metadata = {
  title: "Flerid Technologies",
  description: "...",
  // Icons removed - using App Router favicon.ico
};`}
                </pre>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Troubleshooting */}
        <Card>
          <CardHeader>
            <CardTitle>Troubleshooting Steps</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div>
                <h4 className="font-semibold mb-2">✅ Completed Fixes</h4>
                <ul className="text-sm space-y-1 ml-4">
                  <li>• Updated metadata to use favicons from app/favicons/ directory</li>
                  <li>• Added complete favicon set (16x16, 32x32, 180x180, 192x192, 512x512)</li>
                  <li>• Created web app manifest.json with proper icon references</li>
                  <li>• Added favicon redirect from /favicon.ico to /favicons/favicon.ico</li>
                  <li>• Added cache headers for all favicon files in next.config.ts</li>
                  <li>• Removed old dynamic icon.tsx generator</li>
                </ul>
              </div>
              
              <div>
                <h4 className="font-semibold mb-2">🔍 Additional Checks</h4>
                <ul className="text-sm space-y-1 ml-4">
                  <li>• Clear browser cache and hard refresh (Ctrl+Shift+R)</li>
                  <li>• Check browser developer tools Network tab for favicon requests</li>
                  <li>• Verify favicon file is not corrupted</li>
                  <li>• Restart development server after changes</li>
                </ul>
              </div>
              
              <div>
                <h4 className="font-semibold mb-2">📝 Expected Behavior</h4>
                <ul className="text-sm space-y-1 ml-4">
                  <li>• Favicon should load from /favicon.ico automatically</li>
                  <li>• No 500 errors in browser console</li>
                  <li>• Favicon appears in browser tab</li>
                </ul>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
