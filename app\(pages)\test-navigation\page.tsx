"use client";

import Link from "next/link";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";

export default function TestNavigationPage() {
  const handleClick = (url: string) => {
    console.log(`Attempting to navigate to: ${url}`);
    window.location.href = url;
  };

  return (
    <div className="container mx-auto px-4 py-8">
      <h1 className="text-3xl font-bold mb-8">Navigation Test Page</h1>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Next.js Link Tests */}
        <Card>
          <CardHeader>
            <CardTitle>Next.js Link Components</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <Link 
              href="/services" 
              className="block p-3 bg-blue-100 rounded hover:bg-blue-200 transition-colors"
            >
              → Go to Services Page
            </Link>
            
            <Link 
              href="/services/web-development" 
              className="block p-3 bg-green-100 rounded hover:bg-green-200 transition-colors"
            >
              → Go to Web Development
            </Link>
            
            <Link 
              href="/services/seo" 
              className="block p-3 bg-purple-100 rounded hover:bg-purple-200 transition-colors"
            >
              → Go to SEO Service
            </Link>
          </CardContent>
        </Card>

        {/* Button Tests */}
        <Card>
          <CardHeader>
            <CardTitle>Button Navigation Tests</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <Button 
              onClick={() => handleClick('/services')}
              className="w-full"
            >
              Navigate to Services (JS)
            </Button>
            
            <Button 
              onClick={() => handleClick('/services/web-development')}
              variant="outline"
              className="w-full"
            >
              Navigate to Web Dev (JS)
            </Button>
            
            <Button asChild className="w-full">
              <Link href="/services/seo">
                Navigate to SEO (Button + Link)
              </Link>
            </Button>
          </CardContent>
        </Card>

        {/* Direct HTML Links */}
        <Card>
          <CardHeader>
            <CardTitle>Direct HTML Links</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <a
              href="/services"
              className="block p-3 bg-blue-100 rounded hover:bg-blue-200 transition-colors"
            >
              → Services (HTML Link)
            </a>
            
            <a 
              href="/services/web-development" 
              className="block p-3 bg-red-100 rounded hover:bg-red-200 transition-colors"
            >
              → Web Development (HTML Link)
            </a>
          </CardContent>
        </Card>

        {/* Service Button Text Visibility Test */}
        <Card>
          <CardHeader>
            <CardTitle>Service Button Text Visibility Test</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <p className="text-sm text-muted-foreground">Test buttons with different gradient backgrounds:</p>

              {/* SEO Button Test */}
              <Button
                className="w-full bg-gradient-to-r from-indigo-500 to-blue-500 text-white border-0"
                style={{ textShadow: '0 1px 2px rgba(0, 0, 0, 0.7)' }}
              >
                SEO Service - Learn More (Should be visible)
              </Button>

              {/* Software Development Button Test */}
              <Button
                className="w-full bg-gradient-to-r from-violet-500 to-purple-500 text-white border-0"
                style={{ textShadow: '0 1px 2px rgba(0, 0, 0, 0.7)' }}
              >
                Software Development - Learn More (Should be visible)
              </Button>

              {/* App Development Button Test */}
              <Button
                className="w-full bg-gradient-to-r from-red-500 to-rose-500 text-white border-0"
                style={{ textShadow: '0 1px 2px rgba(0, 0, 0, 0.7)' }}
              >
                App Development - Learn More (Should be visible)
              </Button>

              {/* Get Free Consultation Button Test */}
              <Button
                className="w-full bg-gradient-to-r from-blue-500 to-cyan-500 text-white border-0"
                style={{ textShadow: '0 1px 2px rgba(0, 0, 0, 0.5)' }}
              >
                Get Free Consultation (Should be clearly visible now!)
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Debug Info */}
        <Card>
          <CardHeader>
            <CardTitle>Debug Information</CardTitle>
          </CardHeader>
          <CardContent className="space-y-2 text-sm">
            <div><strong>Current URL:</strong> {typeof window !== 'undefined' ? window.location.href : 'Server'}</div>
            <div><strong>User Agent:</strong> {typeof window !== 'undefined' ? navigator.userAgent.substring(0, 50) + '...' : 'Server'}</div>
            <div><strong>Timestamp:</strong> {new Date().toISOString()}</div>
          </CardContent>
        </Card>
      </div>

      <div className="mt-8 p-4 bg-gray-100 rounded">
        <h2 className="text-xl font-semibold mb-2">Instructions:</h2>
        <ol className="list-decimal list-inside space-y-1">
          <li>Try clicking each link/button above</li>
          <li>Open browser console (F12) to see any errors</li>
          <li>Check which navigation method works</li>
          <li>Test hover effects - background should NOT change unexpectedly</li>
          <li>All elements should be clickable without issues</li>
          <li><strong>Check text visibility</strong> - All button text should be clearly visible</li>
          <li>Test the SEO service card specifically for "Learn More" text visibility</li>
        </ol>
      </div>

      <div className="mt-4 p-4 bg-green-100 rounded">
        <h2 className="text-xl font-semibold mb-2">✅ Recent Fixes Applied:</h2>
        <ul className="list-disc list-inside space-y-1 text-sm">
          <li>Fixed z-index conflicts in service cards</li>
          <li>Added pointer-events-none to background overlays</li>
          <li>Improved CSS isolation with isolate class</li>
          <li>Enhanced button and link z-index positioning</li>
          <li>Resolved hover state interference issues</li>
          <li>Fixed text visibility in all gradient buttons</li>
          <li>Changed "Get Free Consultation" to brighter blue-cyan gradient</li>
          <li>Added Services link to all navigation components</li>
        </ul>
      </div>
    </div>
  );
}
