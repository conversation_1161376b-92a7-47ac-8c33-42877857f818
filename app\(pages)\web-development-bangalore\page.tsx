"use client";

import { motion } from "framer-motion";
import { GradientHeading } from "@/components/ui/gradient-heading";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { GradientButton } from "@/components/ui/gradient-button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { ArrowRight, MapPin, Phone, Mail, CheckCircle, Code, Smartphone, Globe, TrendingUp, Users, Award, Zap } from "lucide-react";
import Link from "next/link";

export default function WebDevelopmentBangalorePage() {
  const services = [
    {
      icon: Code,
      title: "Startup Web Solutions",
      description: "Rapid MVP development and scalable web applications for Bangalore's thriving startup ecosystem"
    },
    {
      icon: Smartphone,
      title: "Tech Product Development",
      description: "Cutting-edge mobile and web applications using latest technologies for tech companies"
    },
    {
      icon: Globe,
      title: "SaaS Platform Development",
      description: "Cloud-based software solutions and platforms for Bangalore's software industry"
    },
    {
      icon: TrendingUp,
      title: "Growth Marketing",
      description: "Data-driven digital marketing strategies for tech startups and established companies"
    }
  ];

  const benefits = [
    "Competitive pricing with Northeast India advantage",
    "Deep understanding of tech startup needs",
    "Agile development methodologies",
    "Latest technology stack expertise",
    "Quick turnaround for MVP development",
    "Scalable architecture for growth"
  ];

  const stats = [
    { icon: Users, number: "75+", label: "Tech Projects" },
    { icon: Award, number: "3+", label: "Years in Tech" },
    { icon: Zap, number: "95%", label: "Client Satisfaction" },
  ];

  return (
    <div className="min-h-screen bg-background">
      {/* Hero Section */}
      <section className="relative py-20 overflow-hidden">
        <div className="absolute inset-0">
          <div className="absolute -top-1/2 -right-1/2 w-[800px] h-[800px] rounded-full bg-blue-500/10 blur-3xl" />
          <div className="absolute -bottom-1/2 -left-1/2 w-[800px] h-[800px] rounded-full bg-cyan-500/10 blur-3xl" />
        </div>

        <div className="container mx-auto px-4 relative">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            className="text-center max-w-4xl mx-auto"
          >
            <GradientHeading
              size="xl"
              gradient="blue"
              className="mb-6"
              as="h1"
            >
              Web Development Company in Bangalore | Startup & Tech Solutions India
            </GradientHeading>
            
            <p className="text-xl text-muted-foreground mb-8 leading-relaxed">
              Professional web development and tech solutions for Bangalore's dynamic startup ecosystem. 
              Flerid Technologies brings innovative development practices from Northeast India to serve India's Silicon Valley with cutting-edge technology solutions.
            </p>

            <div className="flex flex-wrap justify-center gap-4 mb-8">
              <Badge variant="secondary" className="px-4 py-2">
                <MapPin className="w-4 h-4 mr-2" />
                Serving Bangalore & Karnataka
              </Badge>
              <Badge variant="outline" className="px-4 py-2">
                Startup Focused
              </Badge>
              <Badge variant="outline" className="px-4 py-2">
                Latest Tech Stack
              </Badge>
            </div>

            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <GradientButton size="xl" gradient="blue" asChild>
                <Link href="/contact">
                  Get MVP Quote
                  <ArrowRight className="ml-2 h-4 w-4" />
                </Link>
              </GradientButton>
              <Button size="lg" variant="outline" asChild>
                <Link href="/portfolio">View Tech Portfolio</Link>
              </Button>
            </div>
          </motion.div>
        </div>
      </section>

      {/* Stats Section */}
      <section className="py-12 bg-muted/30">
        <div className="container mx-auto px-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {stats.map((stat, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 0.1 * index }}
                className="text-center"
              >
                <stat.icon className="w-8 h-8 text-blue-600 mx-auto mb-2" />
                <div className="text-3xl font-bold text-blue-600 mb-1">{stat.number}</div>
                <div className="text-muted-foreground">{stat.label}</div>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Services Section */}
      <section className="py-16">
        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.2 }}
            className="text-center mb-12"
          >
            <GradientHeading size="lg" gradient="blue" className="mb-4" as="h2">
              Our Services for Bangalore Tech Companies
            </GradientHeading>
            <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
              Specialized web development and digital solutions tailored for Bangalore's startup and technology ecosystem
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {services.map((service, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 0.1 * index }}
              >
                <Card className="h-full hover:shadow-lg transition-shadow">
                  <CardHeader>
                    <service.icon className="w-8 h-8 text-blue-600 mb-2" />
                    <CardTitle className="text-lg">{service.title}</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <p className="text-muted-foreground">{service.description}</p>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Why Choose Us Section */}
      <section className="py-16 bg-muted/30">
        <div className="container mx-auto px-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <motion.div
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.5 }}
            >
              <GradientHeading size="lg" gradient="blue" className="mb-6" as="h2">
                Why Bangalore Startups Choose Flerid Technologies?
              </GradientHeading>
              <p className="text-lg text-muted-foreground mb-6">
                We combine the innovation mindset of Northeast India with deep understanding of Bangalore's tech ecosystem. 
                Our team delivers rapid, scalable solutions that help startups launch faster and scale efficiently.
              </p>
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                {benefits.map((benefit, index) => (
                  <div key={index} className="flex items-center gap-2">
                    <CheckCircle className="w-5 h-5 text-green-600 flex-shrink-0" />
                    <span className="text-sm">{benefit}</span>
                  </div>
                ))}
              </div>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.5, delay: 0.2 }}
            >
              <Card className="p-6 bg-gradient-to-br from-blue-50 to-cyan-50 dark:from-blue-950/30 dark:to-cyan-950/30">
                <CardHeader>
                  <CardTitle className="text-xl">Contact Our Bangalore Tech Team</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex items-center gap-3">
                    <Phone className="w-5 h-5 text-blue-600" />
                    <div>
                      <p className="font-medium">Call Us</p>
                      <p className="text-sm text-muted-foreground">+91-6003351943</p>
                    </div>
                  </div>
                  <div className="flex items-center gap-3">
                    <Mail className="w-5 h-5 text-blue-600" />
                    <div>
                      <p className="font-medium">Email Us</p>
                      <p className="text-sm text-muted-foreground"><EMAIL></p>
                    </div>
                  </div>
                  <div className="flex items-center gap-3">
                    <MapPin className="w-5 h-5 text-blue-600" />
                    <div>
                      <p className="font-medium">Our Location</p>
                      <p className="text-sm text-muted-foreground">Silchar, Assam (Serving Bangalore)</p>
                    </div>
                  </div>
                  <GradientButton className="w-full mt-4" asChild>
                    <Link href="/contact">
                      Start Your Project
                      <ArrowRight className="ml-2 h-4 w-4" />
                    </Link>
                  </GradientButton>
                </CardContent>
              </Card>
            </motion.div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-16 bg-gradient-to-r from-blue-600 to-cyan-600 text-white">
        <div className="container mx-auto px-4 text-center">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
          >
            <h2 className="text-3xl md:text-4xl font-bold mb-4">
              Ready to Launch Your Bangalore Startup?
            </h2>
            <p className="text-xl mb-8 opacity-90">
              Partner with us for rapid MVP development and scalable tech solutions in India's Silicon Valley.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button size="lg" variant="secondary" asChild>
                <Link href="/contact">
                  Get MVP Estimate
                  <ArrowRight className="ml-2 h-4 w-4" />
                </Link>
              </Button>
              <Button size="lg" variant="outline" className="border-white text-white hover:bg-white hover:text-blue-600" asChild>
                <Link href="/portfolio">View Startup Projects</Link>
              </Button>
            </div>
          </motion.div>
        </div>
      </section>
    </div>
  );
}
