"use client";

import { motion } from "framer-motion";
import { GradientHeading } from "@/components/ui/gradient-heading";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { GradientButton } from "@/components/ui/gradient-button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { ArrowRight, MapPin, Phone, Mail, CheckCircle, Code, Smartphone, Globe, TrendingUp } from "lucide-react";
import Link from "next/link";

export default function WebDevelopmentGuwahatiPage() {
  const services = [
    {
      icon: Code,
      title: "Custom Web Development",
      description: "Tailored web solutions for Guwahati businesses using React, Next.js, and modern technologies"
    },
    {
      icon: Smartphone,
      title: "Mobile App Development",
      description: "Native and cross-platform mobile apps for iOS and Android serving Guwahati market"
    },
    {
      icon: Globe,
      title: "E-commerce Solutions",
      description: "Online stores and e-commerce platforms for Guwahati retailers and businesses"
    },
    {
      icon: TrendingUp,
      title: "SEO & Digital Marketing",
      description: "Local SEO and digital marketing services to reach customers in Guwahati and Assam"
    }
  ];

  const benefits = [
    "Local understanding of Guwahati market",
    "Affordable pricing for Assam businesses",
    "Quick turnaround times",
    "Ongoing support and maintenance",
    "Modern, responsive designs",
    "SEO-optimized websites"
  ];

  return (
    <div className="min-h-screen bg-background">
      {/* Hero Section */}
      <section className="relative py-20 overflow-hidden">
        <div className="absolute inset-0">
          <div className="absolute -top-1/2 -right-1/2 w-[800px] h-[800px] rounded-full bg-blue-500/10 blur-3xl" />
          <div className="absolute -bottom-1/2 -left-1/2 w-[800px] h-[800px] rounded-full bg-cyan-500/10 blur-3xl" />
        </div>

        <div className="container mx-auto px-4 relative">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            className="text-center max-w-4xl mx-auto"
          >
            <GradientHeading
              size="xl"
              gradient="blue"
              className="mb-6"
              as="h1"
            >
              Web Development Services in Guwahati, Assam
            </GradientHeading>
            
            <p className="text-xl text-muted-foreground mb-8 leading-relaxed">
              Professional web development and digital marketing services for businesses in Guwahati. 
              Flerid Technologies brings cutting-edge technology solutions from Silchar to serve the greater Assam region.
            </p>

            <div className="flex flex-wrap justify-center gap-4 mb-8">
              <Badge variant="secondary" className="px-4 py-2">
                <MapPin className="w-4 h-4 mr-2" />
                Serving Guwahati & Assam
              </Badge>
              <Badge variant="outline" className="px-4 py-2">
                Based in Silchar
              </Badge>
              <Badge variant="outline" className="px-4 py-2">
                React • Node.js • SEO
              </Badge>
            </div>

            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <GradientButton size="xl" gradient="blue" asChild>
                <Link href="/contact">
                  Get Free Quote
                  <ArrowRight className="ml-2 h-4 w-4" />
                </Link>
              </GradientButton>
              <Button size="lg" variant="outline" asChild>
                <Link href="/portfolio">View Our Work</Link>
              </Button>
            </div>
          </motion.div>
        </div>
      </section>

      {/* Services Section */}
      <section className="py-16 bg-muted/30">
        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.2 }}
            className="text-center mb-12"
          >
            <GradientHeading size="lg" gradient="blue" className="mb-4" as="h2">
              Our Services in Guwahati
            </GradientHeading>
            <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
              Comprehensive web development and digital marketing solutions for Guwahati businesses
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {services.map((service, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 0.1 * index }}
              >
                <Card className="h-full hover:shadow-lg transition-shadow">
                  <CardHeader>
                    <service.icon className="w-8 h-8 text-blue-600 mb-2" />
                    <CardTitle className="text-lg">{service.title}</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <p className="text-muted-foreground">{service.description}</p>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Why Choose Us Section */}
      <section className="py-16">
        <div className="container mx-auto px-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <motion.div
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.5 }}
            >
              <GradientHeading size="lg" gradient="blue" className="mb-6" as="h2">
                Why Choose Flerid Technologies for Your Guwahati Business?
              </GradientHeading>
              <p className="text-lg text-muted-foreground mb-6">
                Based in Silchar with deep understanding of the Assam market, we provide personalized 
                web development services that help Guwahati businesses succeed online.
              </p>
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                {benefits.map((benefit, index) => (
                  <div key={index} className="flex items-center gap-2">
                    <CheckCircle className="w-5 h-5 text-green-600 flex-shrink-0" />
                    <span className="text-sm">{benefit}</span>
                  </div>
                ))}
              </div>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.5, delay: 0.2 }}
            >
              <Card className="p-6 bg-gradient-to-br from-blue-50 to-cyan-50 dark:from-blue-950/30 dark:to-cyan-950/30">
                <CardHeader>
                  <CardTitle className="text-xl">Contact Our Guwahati Team</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex items-center gap-3">
                    <Phone className="w-5 h-5 text-blue-600" />
                    <div>
                      <p className="font-medium">Call Us</p>
                      <p className="text-sm text-muted-foreground">+91-6003351943</p>
                    </div>
                  </div>
                  <div className="flex items-center gap-3">
                    <Mail className="w-5 h-5 text-blue-600" />
                    <div>
                      <p className="font-medium">Email Us</p>
                      <p className="text-sm text-muted-foreground"><EMAIL></p>
                    </div>
                  </div>
                  <div className="flex items-center gap-3">
                    <MapPin className="w-5 h-5 text-blue-600" />
                    <div>
                      <p className="font-medium">Our Location</p>
                      <p className="text-sm text-muted-foreground">Silchar, Assam (Serving Guwahati)</p>
                    </div>
                  </div>
                  <GradientButton className="w-full mt-4" asChild>
                    <Link href="/contact">
                      Get Started Today
                      <ArrowRight className="ml-2 h-4 w-4" />
                    </Link>
                  </GradientButton>
                </CardContent>
              </Card>
            </motion.div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-16 bg-gradient-to-r from-blue-600 to-cyan-600 text-white">
        <div className="container mx-auto px-4 text-center">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
          >
            <h2 className="text-3xl md:text-4xl font-bold mb-4">
              Ready to Grow Your Guwahati Business Online?
            </h2>
            <p className="text-xl mb-8 opacity-90">
              Let's discuss your web development project and create a digital solution that drives results.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button size="lg" variant="secondary" asChild>
                <Link href="/contact">
                  Get Free Consultation
                  <ArrowRight className="ml-2 h-4 w-4" />
                </Link>
              </Button>
              <Button size="lg" variant="outline" className="border-white text-white hover:bg-white hover:text-blue-600" asChild>
                <Link href="/portfolio">View Portfolio</Link>
              </Button>
            </div>
          </motion.div>
        </div>
      </section>
    </div>
  );
}
