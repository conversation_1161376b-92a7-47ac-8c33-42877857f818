import { NextRequest, NextResponse } from 'next/server';
import fs from 'fs';
import path from 'path';

export async function GET(request: NextRequest) {
  try {
    // Serve the favicon.ico as apple-touch-icon.png
    const faviconPath = path.join(process.cwd(), 'app', 'favicon.ico');
    
    if (fs.existsSync(faviconPath)) {
      const favicon = fs.readFileSync(faviconPath);
      
      return new NextResponse(favicon, {
        status: 200,
        headers: {
          'Content-Type': 'image/x-icon',
          'Cache-Control': 'public, max-age=31536000, immutable',
        },
      });
    }
    
    // If favicon doesn't exist, return 404
    return new NextResponse('Not Found', { status: 404 });
  } catch (error) {
    console.error('Error serving apple-touch-icon:', error);
    return new NextResponse('Internal Server Error', { status: 500 });
  }
}
