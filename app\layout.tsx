import type { <PERSON><PERSON><PERSON> } from "next";
import { <PERSON><PERSON><PERSON> } from "next/font/google";
import "./globals.css";
import { ThemeProvider } from "next-themes";
import { Toaster } from "@/components/ui/sonner";
import { AppSidebar } from "@/components/app-sidebar";
import Footer from "@/components/footer";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

export const metadata: Metadata = {
  metadataBase: new URL('https://www.flerid.com'),
  title: {
    default: "Flerid Technologies | Web Development Company in Silchar, Assam, India",
    template: "%s | Flerid Technologies"
  },
  description: "Leading web development and digital marketing company in Silchar, Assam, India. We provide custom web development, mobile app development, SEO services, and digital marketing solutions for businesses across India and globally.",
  keywords: [
    "web development company Silchar",
    "digital marketing agency Assam",
    "web development Silchar Assam",
    "SEO services India",
    "mobile app development Silchar",
    "software development company India",
    "Flerid Technologies",
    "web design Si<PERSON><PERSON><PERSON>",
    "digital marketing services Assam",
    "custom web development India",
    "e-commerce development Silchar",
    "React development India",
    "Node.js development Assam"
  ],
  authors: [{ name: "Flerid Technologies", url: "https://www.flerid.com" }],
  creator: "Flerid Technologies",
  publisher: "Flerid Technologies",
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      "max-video-preview": -1,
      "max-image-preview": "large",
      "max-snippet": -1,
    },
  },
  openGraph: {
    type: "website",
    locale: "en_IN",
    url: "https://www.flerid.com",
    title: "Flerid Technologies | Web Development Company in Silchar, Assam, India",
    description: "Leading web development and digital marketing company in Silchar, Assam, India. Custom web development, mobile apps, SEO, and digital marketing solutions.",
    siteName: "Flerid Technologies",
    images: [
      {
        url: "/og-flerid-home.jpg",
        width: 1200,
        height: 630,
        alt: "Flerid Technologies - Web Development Company in Silchar, Assam, India",
      },
    ],
  },
  twitter: {
    card: "summary_large_image",
    title: "Flerid Technologies | Web Development Company in Silchar, Assam, India",
    description: "Leading web development and digital marketing company in Silchar, Assam, India. Custom solutions for businesses worldwide.",
    images: ["/og-flerid-home.jpg"],
    creator: "@fleridtech",
  },
  alternates: {
    canonical: "https://www.flerid.com",
  },
  other: {
    "geo.region": "IN-AS",
    "geo.placename": "Silchar, Assam, India",
    "geo.position": "24.8333;92.7789",
    "ICBM": "24.8333, 92.7789",
    "business:contact_data:street_address": "Silchar",
    "business:contact_data:locality": "Silchar",
    "business:contact_data:region": "Assam",
    "business:contact_data:postal_code": "788001",
    "business:contact_data:country_name": "India",
  },
  manifest: "/manifest.webmanifest",
  icons: {
    icon: [
      { url: '/favicon.ico', sizes: '32x32', type: 'image/x-icon' },
      { url: '/favicon.ico', sizes: '16x16', type: 'image/x-icon' },
    ],
    shortcut: '/favicon.ico',
    apple: [
      { url: '/apple-touch-icon.png', sizes: '180x180', type: 'image/png' },
    ],
    other: [
      {
        rel: 'apple-touch-icon-precomposed',
        url: '/apple-touch-icon.png',
      },
    ],
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  // LocalBusiness structured data
  const localBusinessSchema = {
    "@context": "https://schema.org",
    "@type": "LocalBusiness",
    "@id": "https://www.flerid.com/#organization",
    "name": "Flerid Technologies",
    "alternateName": "Flerid",
    "description": "Leading web development and digital marketing company in Silchar, Assam, India providing custom web development, mobile app development, SEO services, and digital marketing solutions.",
    "url": "https://www.flerid.com",
    "telephone": "+91-6003351943",
    "email": "<EMAIL>",
    "foundingDate": "2022",
    "slogan": "Transforming Ideas into Digital Reality",
    "address": {
      "@type": "PostalAddress",
      "addressLocality": "Silchar",
      "addressRegion": "Assam",
      "postalCode": "788001",
      "addressCountry": "IN"
    },
    "geo": {
      "@type": "GeoCoordinates",
      "latitude": "24.8333",
      "longitude": "92.7789"
    },
    "areaServed": [
      {
        "@type": "Country",
        "name": "India"
      },
      {
        "@type": "State",
        "name": "Assam"
      },
      {
        "@type": "City",
        "name": "Silchar"
      }
    ],
    "serviceArea": {
      "@type": "GeoCircle",
      "geoMidpoint": {
        "@type": "GeoCoordinates",
        "latitude": "24.8333",
        "longitude": "92.7789"
      },
      "geoRadius": "50000"
    },
    "hasOfferCatalog": {
      "@type": "OfferCatalog",
      "name": "Web Development and Digital Marketing Services",
      "itemListElement": [
        {
          "@type": "Offer",
          "itemOffered": {
            "@type": "Service",
            "name": "Web Development",
            "description": "Custom web development services using React, Next.js, Node.js"
          }
        },
        {
          "@type": "Offer",
          "itemOffered": {
            "@type": "Service",
            "name": "Mobile App Development",
            "description": "Native and cross-platform mobile app development"
          }
        },
        {
          "@type": "Offer",
          "itemOffered": {
            "@type": "Service",
            "name": "Digital Marketing",
            "description": "SEO, social media marketing, and digital advertising services"
          }
        }
      ]
    },
    "sameAs": [
      "https://www.linkedin.com/company/flerid-technologies",
      "https://www.facebook.com/fleridtechnologies",
      "https://twitter.com/fleridtech"
    ]
  };

  return (
    <html lang="en-IN" suppressHydrationWarning>
      <body
        className={`${geistSans.variable} antialiased min-h-screen`}
        suppressHydrationWarning
      >
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{ __html: JSON.stringify(localBusinessSchema) }}
        />
        <ThemeProvider
          attribute="class"
          defaultTheme="dark"
          enableSystem
          disableTransitionOnChange
        >
          <div className="relative flex min-h-screen">
            {/* Sidebar */}
            <div className="fixed left-0 top-0 z-40 h-screen">
              <AppSidebar />
            </div>

            {/* Main Content */}
            <div className="flex-1 flex flex-col md:pl-[60px] transition-all duration-300">
              <main className="flex-1 min-h-screen w-full">
                <Toaster
                  position="top-right"
                  closeButton
                />
                {/* <BackgroundLines> */}
                  <div>
                    {children}
                  </div>
                  <Footer />
                {/* </BackgroundLines> */}
              </main>
            </div>
          </div>
        </ThemeProvider>
      </body>
    </html>
  );
}