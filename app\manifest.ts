import { MetadataRoute } from 'next'

export default function manifest(): MetadataRoute.Manifest {
  return {
    name: 'Flerid Technologies - Web Development Company in Silchar, Assam, India',
    short_name: 'Flerid Technologies',
    description: 'Leading web development and digital marketing company in Silchar, Assam, India. Custom web development, mobile app development, SEO services, and digital marketing solutions.',
    start_url: '/',
    display: 'standalone',
    background_color: '#ffffff',
    theme_color: '#2563eb',
    orientation: 'portrait-primary',
    scope: '/',
    lang: 'en-IN',
    categories: ['business', 'productivity', 'technology'],
    icons: [
      {
        src: '/favicon.ico',
        sizes: '16x16 32x32',
        type: 'image/x-icon',
      },
      {
        src: '/apple-touch-icon.png',
        sizes: '180x180',
        type: 'image/png',
        purpose: 'any maskable',
      },
    ],
    shortcuts: [
      {
        name: 'Services',
        short_name: 'Services',
        description: 'View our web development and digital marketing services',
        url: '/services',
        icons: [
          {
            src: '/favicon.ico',
            sizes: '32x32',
          },
        ],
      },
      {
        name: 'Portfolio',
        short_name: 'Portfolio',
        description: 'View our web development portfolio',
        url: '/portfolio',
        icons: [
          {
            src: '/favicon.ico',
            sizes: '32x32',
          },
        ],
      },
      {
        name: 'Contact',
        short_name: 'Contact',
        description: 'Contact Flerid Technologies',
        url: '/contact',
        icons: [
          {
            src: '/favicon.ico',
            sizes: '32x32',
          },
        ],
      },
    ],
  }
}
