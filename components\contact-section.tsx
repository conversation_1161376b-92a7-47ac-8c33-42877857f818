"use client";

import { useState, useRef } from "react";
import { motion, useInView } from "framer-motion";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import * as z from "zod";
import { But<PERSON> } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { cn } from "@/lib/utils";
import { Mail, Phone, MapPin, Send } from "lucide-react";

// Form validation schema
const formSchema = z.object({
  name: z.string().min(2, "Name must be at least 2 characters"),
  email: z.string().email("Invalid email address"),
  message: z.string().min(10, "Message must be at least 10 characters"),
});

export default function ContactSection() {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const sectionRef = useRef(null);
  const isInView = useInView(sectionRef, { once: false, amount: 0.2 });

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
        delayChildren: 0.3,
      },
    },
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: { duration: 0.5, ease: "easeOut" },
    },
  };

  // Form handling
  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      name: "",
      email: "",
      message: "",
    },
  });

  function onSubmit(data: z.infer<typeof formSchema>) {
    setIsSubmitting(true);
    
    // Simulate form submission
    setTimeout(() => {
      console.log(data);
      setIsSubmitting(false);
      form.reset();
      // In a real application, you would send this data to your backend
    }, 1500);
  }

  // Contact information
  const contactInfo = [
    {
      icon: <MapPin className="h-6 w-6" />,
      title: "Address",
      details: "Silchar, Assam, India",
    },
    {
      icon: <Mail className="h-6 w-6" />,
      title: "Email",
      details: "<EMAIL>",
    },
    {
      icon: <Phone className="h-6 w-6" />,
      title: "Phone",
      details: "+91 6003351943",
    },
  ];

  return (
    <section id="contact" className="py-24 relative overflow-hidden" ref={sectionRef}>
      {/* Background Elements */}
      <div className="absolute inset-0 overflow-hidden">
        {/* Gradient background */}
        <div className="absolute inset-0 bg-gradient-to-b from-white to-soft-white dark:from-background dark:to-navy/5 z-0"></div>
        
        {/* Animated background shapes */}
        <motion.div 
          className="absolute top-40 right-10 w-64 h-64 rounded-full bg-teal/5 dark:bg-teal/5 blur-3xl"
          animate={{ 
            x: [0, 10, 0], 
            y: [0, -15, 0],
          }}
          transition={{ 
            duration: 12, 
            ease: "easeInOut", 
            repeat: Infinity,
          }}
        />
      </div>
      
      <div className="container mx-auto px-4 relative z-10">
        <motion.div 
          className="max-w-6xl mx-auto"
          variants={containerVariants}
          initial="hidden"
          animate={isInView ? "visible" : "hidden"}
        >
          {/* Section Header */}
          <motion.div className="text-center mb-16" variants={itemVariants}>
            <h2 className="text-3xl md:text-5xl font-bold mb-6 text-navy dark:text-soft-white">
              Get In Touch
            </h2>
            <p className="text-lg md:text-xl text-warm-gray max-w-3xl mx-auto">
              Have a question or want to work together? Reach out to us and we'll get back to you as soon as possible.
            </p>
          </motion.div>

          {/* Contact Grid */}
          <div className="grid md:grid-cols-2 gap-12 items-start">
            {/* Contact Form */}
            <motion.div variants={itemVariants}>
              <div className="bg-white/80 dark:bg-navy/20 backdrop-blur-sm p-8 rounded-xl border-2 border-navy/10 dark:border-teal/10 shadow-sm">
                <h3 className="text-2xl font-semibold mb-6 text-navy dark:text-soft-white">
                  Send Us a Message
                </h3>
                
                <Form {...form}>
                  <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
                    <FormField
                      control={form.control}
                      name="name"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel className="text-navy dark:text-soft-white">Name</FormLabel>
                          <FormControl>
                            <Input 
                              placeholder="Your name" 
                              {...field} 
                              className="bg-white/50 dark:bg-navy/30 border-navy/10 dark:border-teal/10"
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    
                    <FormField
                      control={form.control}
                      name="email"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel className="text-navy dark:text-soft-white">Email</FormLabel>
                          <FormControl>
                            <Input 
                              placeholder="Your email" 
                              type="email" 
                              {...field} 
                              className="bg-white/50 dark:bg-navy/30 border-navy/10 dark:border-teal/10"
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    
                    <FormField
                      control={form.control}
                      name="message"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel className="text-navy dark:text-soft-white">Message</FormLabel>
                          <FormControl>
                            <Textarea 
                              placeholder="Your message" 
                              {...field} 
                              className="min-h-[120px] bg-white/50 dark:bg-navy/30 border-navy/10 dark:border-teal/10"
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    
                    <Button 
                      type="submit" 
                      disabled={isSubmitting}
                      className={cn(
                        "w-full py-6",
                        "bg-gradient-to-r from-navy to-teal",
                        "hover:from-navy/90 hover:to-teal/90",
                        "text-white shadow-md"
                      )}
                    >
                      {isSubmitting ? (
                        <span className="flex items-center">
                          <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                            <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                          </svg>
                          Sending...
                        </span>
                      ) : (
                        <span className="flex items-center">
                          Send Message
                          <Send className="ml-2 h-5 w-5" />
                        </span>
                      )}
                    </Button>
                  </form>
                </Form>
              </div>
            </motion.div>
            
            {/* Contact Information */}
            <motion.div variants={itemVariants} className="flex flex-col gap-8">
              {/* Contact Cards */}
              {contactInfo.map((item, index) => (
                <motion.div 
                  key={item.title}
                  whileHover={{ y: -5, transition: { duration: 0.2 } }}
                  className="bg-white/80 dark:bg-navy/20 backdrop-blur-sm p-6 rounded-xl border border-transparent hover:border-teal/20 shadow-sm flex items-start gap-4"
                >
                  <div className="bg-gradient-to-br from-navy to-teal p-3 rounded-full text-white">
                    {item.icon}
                  </div>
                  <div>
                    <h4 className="text-xl font-semibold mb-1 text-navy dark:text-soft-white">{item.title}</h4>
                    <p className="text-warm-gray">{item.details}</p>
                  </div>
                </motion.div>
              ))}
              
              {/* Map or Additional Info */}
              <motion.div 
                className="bg-white/80 dark:bg-navy/20 backdrop-blur-sm p-6 rounded-xl border-2 border-navy/10 dark:border-teal/10 shadow-sm mt-auto"
                whileHover={{ y: -5, transition: { duration: 0.2 } }}
              >
                <h4 className="text-xl font-semibold mb-4 text-navy dark:text-soft-white">Working Hours</h4>
                <ul className="space-y-2 text-warm-gray">
                  <li className="flex justify-between">
                    <span>Monday - Friday:</span>
                    <span>9:00 AM - 6:00 PM</span>
                  </li>
                  <li className="flex justify-between">
                    <span>Saturday:</span>
                    <span>9:00 AM - 1:00 PM</span>
                  </li>
                  <li className="flex justify-between">
                    <span>Sunday:</span>
                    <span>Closed</span>
                  </li>
                </ul>
              </motion.div>
            </motion.div>
          </div>
        </motion.div>
      </div>
    </section>
  );
}
