"use client";

import Link from "next/link";
import { motion } from "framer-motion";
import {
  Facebook,
  Twitter,
  Mail,
  Instagram,
  Linkedin,
  Home,
  Phone,
  ArrowUp
} from "lucide-react";
import { Button } from "./ui/button";
import { GradientHeading } from "./ui/gradient-heading";
import { getServiceUrl } from "@/lib/data";

const socialLinks = [
  { icon: Facebook, href: "https://www.facebook.com/flerid04", label: "Facebook" },
  { icon: Twitter, href: "https://x.com/FleridTech?t=SsP9kExECEDUin55L-cNlw&s=08", label: "Twitter" },
  { icon: Mail, href: "mailto:<EMAIL>", label: "Email" },
  { icon: Instagram, href: "https://www.instagram.com/fleridtechnology/", label: "Instagram" },
  { icon: Linkedin, href: "https://x.com/FleridTech?t=SsP9kExECEDUin55L-cNlw&s=08", label: "LinkedIn" }
];

const services = [
  "Web Design & Development",
  "Social Media Marketing",
  "SEO",
  "Mobile App Development",
  "Software Development"
];

const links = [
  { label: "Home", href: "/" },
  { label: "About", href: "/about" },
  { label: "Services", href: "/services" },
  { label: "Portfolio", href: "/portfolio" },
  { label: "Pricing", href: "/pricing" },
  { label: "Blogs", href: "/blogs" },
  { label: "Contact", href: "/contact" },
];

export default function Footer() {
  const scrollToTop = () => {
    window.scrollTo({ top: 0, behavior: "smooth" });
  };

  return (
    <footer className="relative">
      {/* Social Media Banner */}
      <div className="bg-gradient-to-r from-blue-600 to-cyan-600 dark:from-blue-700 dark:to-cyan-700">
        <div className="container mx-auto px-4 py-3">
          <div className="flex flex-wrap items-center justify-between gap-4">
            <p className="text-sm sm:text-base text-white">
              Get connected with us on social media:
            </p>
            <div className="flex items-center gap-4">
              {socialLinks.map((social) => (
                <motion.a
                  key={social.label}
                  href={social.href}
                  whileHover={{ scale: 1.1 }}
                  whileTap={{ scale: 0.95 }}
                  className="text-white/90 hover:text-white transition-colors"
                  aria-label={social.label}
                >
                  <social.icon className="w-4 h-4" />
                </motion.a>
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* Main Footer */}
      <div className="bg-card text-card-foreground border-t">
        <div className="container mx-auto px-4 py-12">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {/* Company Info */}
            <div className="space-y-4">
              <div className="relative mb-4">
                <GradientHeading
                  size="sm"
                  gradient="blue"
                  className="mb-0"
                  as="h2"
                >
                  FLERID TECHNOLOGIES
                </GradientHeading>
                <span className="absolute -bottom-2 left-0 w-12 h-0.5 bg-gradient-to-r from-blue-600 to-cyan-600" />
              </div>
              <p className="text-sm leading-relaxed text-muted-foreground">
                Innovative solutions, seamless experiences, and cutting-edge technology. 
                Transforming businesses through digital excellence. Your success, our mission.
              </p>
            </div>

            {/* Useful Links */}
            <div>
              <GradientHeading
                size="sm"
                gradient="blue"
                className="mb-4"
                as="h3"
              >
                Useful Links
              </GradientHeading>
              <ul className="space-y-2">
                {links.map((link) => (
                  <li key={link.label}>
                    <Link 
                      href={link.href}
                      className="text-muted-foreground hover:text-foreground transition-colors inline-flex items-center gap-2 group"
                    >
                      <span className="w-2 h-2 rounded-full bg-gradient-to-r from-blue-600 to-cyan-600 group-hover:w-3 transition-all" />
                      {link.label}
                    </Link>
                  </li>
                ))}
              </ul>
            </div>

            {/* Services */}
            <div>
              <GradientHeading
                size="sm"
                gradient="green"
                className="mb-4"
                as="h3"
              >
                Services
              </GradientHeading>
              <ul className="space-y-2">
                {services.map((service) => (
                  <li key={service}>
                    <Link
                      href={getServiceUrl(service)}
                      className="text-muted-foreground hover:text-foreground transition-colors inline-flex items-center gap-2 group"
                    >
                      <span className="w-2 h-2 rounded-full bg-gradient-to-r from-blue-600 to-cyan-600 group-hover:w-3 transition-all" />
                      {service}
                    </Link>
                  </li>
                ))}
                <li>
                  <Link
                    href="/services"
                    className="text-primary hover:text-primary/80 transition-colors inline-flex items-center gap-2 group font-medium"
                  >
                    <span className="w-2 h-2 rounded-full bg-gradient-to-r from-blue-600 to-cyan-600 group-hover:w-3 transition-all" />
                    View All Services
                  </Link>
                </li>
              </ul>
            </div>

            {/* Contact */}
            <div>
              <GradientHeading
                size="sm"
                gradient="purple"
                className="mb-4"
                as="h3"
              >
                Contact
              </GradientHeading>
              <ul className="space-y-3 text-muted-foreground">
                <li className="flex items-start gap-3">
                  <Home className="w-5 h-5 text-blue-600 dark:text-blue-500 shrink-0 mt-1" />
                  <span>Silchar, Assam, India</span>
                </li>
                <li>
                  <a 
                    href="mailto:<EMAIL>" 
                    className="flex items-start gap-3 hover:text-foreground transition-colors"
                  >
                    <Mail className="w-5 h-5 text-blue-600 dark:text-blue-500 shrink-0 mt-1" />
                    <EMAIL>
                  </a>
                </li>
                <li>
                  <a 
                    href="tel:+************" 
                    className="flex items-start gap-3 hover:text-foreground transition-colors"
                  >
                    <Phone className="w-5 h-5 text-blue-600 dark:text-blue-500 shrink-0 mt-1" />
                    +91 6003351943
                  </a>
                </li>
                <li>
                  <a 
                    href="tel:+7002135973" 
                    className="flex items-start gap-3 hover:text-foreground transition-colors"
                  >
                    <Phone className="w-5 h-5 text-blue-600 dark:text-blue-500 shrink-0 mt-1" />
                    + 91 7002135973
                  </a>
                </li>
                <li>
                  <a 
                    href="tel:+6901527553" 
                    className="flex items-start gap-3 hover:text-foreground transition-colors"
                  >
                    <Phone className="w-5 h-5 text-blue-600 dark:text-blue-500 shrink-0 mt-1" />
                    + 91 6901527553
                  </a>
                </li>
              </ul>
            </div>
          </div>
        </div>
      </div>

      {/* Copyright */}
      <div className="bg-muted/50">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <p className="text-sm text-muted-foreground">
              © 2025 Copyright:
              <Link 
                href="/" 
                className="text-blue-600 hover:text-blue-500 dark:text-blue-500 dark:hover:text-blue-400 ml-1"
              >
                Flerid Technology
              </Link>
            </p>
            <Button
              variant="ghost"
              size="icon"
              onClick={scrollToTop}
              className="text-blue-600 hover:text-blue-500 dark:text-blue-500 dark:hover:text-blue-400"
            >
              <ArrowUp className="w-5 h-5" />
            </Button>
          </div>
        </div>
      </div>
    </footer>
  );
}