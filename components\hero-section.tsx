"use client";

import { But<PERSON> } from "@/components/ui/button";
import { GradientButton } from "@/components/ui/gradient-button";
import { GradientHeading } from "@/components/ui/gradient-heading";
import { Badge } from "@/components/ui/badge";
import { ArrowRight } from "lucide-react";
import Link from "next/link";
import { motion } from "framer-motion";

const HeroSection = () => {
  return (
    <div className="min-h-full">
      {/* Hero Section */}
      <section className="relative pt-24 pb-20 overflow-hidden">
        <div className="container mx-auto px-4 py-8">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="max-w-4xl mx-auto text-center"
          >
            {/* Main Content - SEO Optimized H1 */}
            <GradientHeading
              size="xl"
              gradient="blue"
              className="mb-6"
              as="h1"
            >
              Web Development Company in Silchar, Assam | Custom Software Solutions India
            </GradientHeading>

            <p className="text-xl md:text-2xl text-muted-foreground mb-8 leading-relaxed">
              Leading web development and digital marketing company in Silchar, Assam, India.
              We create custom websites, mobile apps, and provide SEO services for businesses across India and globally.
              <span className="italic block mt-2">&quot;Transforming Ideas into Digital Reality&quot;</span>
            </p>

            {/* Badges - SEO Keywords */}
            <div className="flex flex-wrap justify-center gap-4 mb-12">
              <Badge variant="secondary" className="px-4 py-2">
                Silchar, Assam Based
              </Badge>
              <Badge variant="outline" className="px-4 py-2">
                Serving India & Global
              </Badge>
              <Badge variant="outline" className="px-4 py-2">
                React • Node.js • SEO
              </Badge>
              <Badge variant="outline" className="px-4 py-2">
                Since 2022
              </Badge>
            </div>

            {/* CTA Buttons */}
            <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
              <GradientButton
                size="xl"
                gradient="blue"
                onClick={() => {
                  // Add smooth scrolling behavior
                  document.getElementById("booking")?.scrollIntoView({
                    behavior: "smooth",
                  });
                }}
                className="service-button-text flex items-center"
              >
                Get Started
                <ArrowRight className="ml-2 h-4 w-4 transition-transform group-hover:translate-x-1" />
              </GradientButton>

              <Button
                size="lg"
                variant="outline"
                className="h-12 px-8"
                asChild
              >
                <Link href="/services">
                  View Services
                </Link>
              </Button>
            </div>
          </motion.div>
        </div>
      </section>
    </div>
  );
};

export default HeroSection;
