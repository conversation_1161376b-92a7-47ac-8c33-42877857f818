"use client";

import { useEffect, useState } from "react";
import { motion } from "framer-motion";
import Link from "next/link";
import { ArrowRight, ExternalLink, Loader2 } from "lucide-react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Meteors } from "./ui/meteors";
import { PlaceholderImage } from "./ui/placeholder-image";
import { GradientHeading } from "./ui/gradient-heading";
import { cn } from "@/lib/utils";
import {
  PortfolioItem,
  fetchPortfolioItems,
  getPortfolioImageUrl,
} from "@/lib/appwrite";

// Gradient colors for different categories using Flerid's color palette
const gradientMap: Record<string, string> = {
  "Web Development": "from-navy to-teal",
  "Mobile App": "from-teal to-navy",
  "UI/UX Design": "from-navy to-warm-gray",
  "E-commerce": "from-teal to-warm-gray",
  Default: "from-navy to-teal",
};

const cardVariants = {
  hidden: { opacity: 0, y: 20 },
  visible: (i: number) => ({
    opacity: 1,
    y: 0,
    transition: {
      delay: i * 0.1,
      duration: 0.5,
      ease: "easeOut",
    },
  }),
};

const PortfolioSection = () => {
  const [portfolioItems, setPortfolioItems] = useState<PortfolioItem[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const fetchPortfolio = async () => {
      try {
        // Fetch portfolio items - sorted by creation date (newest first)
        const items = await fetchPortfolioItems();
        // Limit to 4 items for the homepage section (will show the 4 newest items)
        setPortfolioItems(items.slice(0, 4));
      } catch (error) {
        console.error("Error fetching portfolio items:", error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchPortfolio();
  }, []);

  // Function to determine the grid class based on index
  const getGridClass = (index: number) => {
    const classes = [
      "md:col-span-1", // First item
      "md:col-span-1", // Second item
      "md:col-span-1", // Third item
      "md:col-span-1", // Fourth item
    ];
    return classes[index] || "md:col-span-1";
  };

  return (
    <section
      id="portfolio"
      className="py-12 relative overflow-hidden"
    >
      <div className="container mx-auto px-4">
        {/* Section Header */}
        <div className="text-center mb-16">
          <motion.div
            initial={{ opacity: 0, y: -20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
          >
            <GradientHeading
              size="lg"
              gradient="blue"
              className="mb-4"
              as="h2"
            >
              Our Portfolio
            </GradientHeading>
          </motion.div>
          <motion.p
            initial={{ opacity: 0, y: -20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ delay: 0.2 }}
            className="dark:text-warm-gray text-navy/70 text-lg max-w-2xl mx-auto"
          >
            Explore some of our recent projects and see how we&apos;ve helped
            businesses achieve their digital goals
          </motion.p>
        </div>

        {isLoading ? (
          <div className="flex justify-center items-center py-20">
            <Loader2 className="w-8 h-8 animate-spin text-primary" />
          </div>
        ) : portfolioItems.length === 0 ? (
          <div className="text-center py-10">
            <p className="text-muted-foreground">
              No portfolio items found. Check back soon!
            </p>
          </div>
        ) : (
          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6 max-w-5xl mx-auto">
            {portfolioItems.map((project, index) => (
              <motion.div
                key={project.$id}
                custom={index}
                initial="hidden"
                whileInView="visible"
                viewport={{ once: true }}
                variants={cardVariants}
                className={getGridClass(index)}
              >
                <Card
                  className={cn(
                    `group relative overflow-hidden border-2 border-warm-gray/30 hover:border-teal transition-colors h-full transition-all duration-300 shadow-md hover:shadow-lg hover:shadow-teal/10 bg-soft-white dark:bg-navy/80`
                  )}
                >
                  {/* Project Image */}
                  <div className="relative h-56 w-full overflow-hidden">
                    <PlaceholderImage
                      src={project.image ? getPortfolioImageUrl(project.image) : "/placeholder-project.svg"}
                      alt={project.title}
                      fill
                      className="object-cover object-center transition-transform duration-500 group-hover:scale-105"
                    />
                    <div
                      className={cn(
                        "absolute inset-0 opacity-40 bg-gradient-to-t",
                        gradientMap[project.category] || gradientMap["Default"]
                      )}
                    />
                  </div>

                  <CardHeader className="p-4 pb-2">
                    <CardTitle className="text-lg mb-1 dark:text-soft-white text-navy">
                      {project.title}
                    </CardTitle>
                    <CardDescription className="text-sm dark:text-warm-gray text-navy/70 line-clamp-2">
                      {project.description}
                    </CardDescription>
                  </CardHeader>

                  <CardContent className="z-10 p-4 pt-0">
                    {/* Features */}
                    {project.features && project.features.length > 0 && (
                      <div className="mb-4">
                        <h4 className="text-xs font-semibold uppercase tracking-wider text-navy dark:text-soft-white/70 mb-2">Key Features</h4>
                        <div className="flex flex-wrap gap-1.5 mb-2">
                          {project.features.map((feature, idx) => {
                            // Generate a consistent color based on the feature title
                            const featureColors = [
                              "bg-teal/20 dark:bg-teal/40 text-teal-800 dark:text-teal-100 border-teal/30",
                              "bg-navy/10 dark:bg-navy/40 text-navy dark:text-navy-100 border-navy/30",
                              "bg-blue-100 dark:bg-blue-900/40 text-blue-800 dark:text-blue-100 border-blue-200",
                              "bg-purple-100 dark:bg-purple-900/40 text-purple-800 dark:text-purple-100 border-purple-200",
                              "bg-indigo-100 dark:bg-indigo-900/40 text-indigo-800 dark:text-indigo-100 border-indigo-200",
                              "bg-emerald-100 dark:bg-emerald-900/40 text-emerald-800 dark:text-emerald-100 border-emerald-200",
                              "bg-rose-100 dark:bg-rose-900/40 text-rose-800 dark:text-rose-100 border-rose-200",
                            ];
                            const colorIndex = Math.abs(feature.title.split('').reduce((acc, char) => acc + char.charCodeAt(0), 0)) % featureColors.length;

                            return (
                              <div
                                key={idx}
                                className={`${featureColors[colorIndex]} px-2.5 py-1 rounded-md text-xs border flex items-center gap-1.5`}
                                title={feature.description}
                              >
                                <span className="font-medium">{feature.title}</span>
                              </div>
                            );
                          })}
                        </div>
                      </div>
                    )}

                    {/* Technologies */}
                    <div className="mb-4">
                      <h4 className="text-xs font-semibold uppercase tracking-wider text-navy dark:text-soft-white/70 mb-2">Technologies</h4>
                      <div className="flex flex-wrap gap-1.5">
                        {project.technologies.map((tech) => {
                          // Generate a consistent color based on the technology name
                          const colors = [
                            "bg-navy/10 dark:bg-navy/30 text-navy dark:text-soft-white",
                            "bg-teal/10 dark:bg-teal/30 text-teal-800 dark:text-teal-200",
                            "bg-warm-gray/20 dark:bg-warm-gray/30 text-gray-700 dark:text-warm-gray",
                            "bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-200",
                            "bg-purple-100 dark:bg-purple-900/30 text-purple-800 dark:text-purple-200",
                            "bg-amber-100 dark:bg-amber-900/30 text-amber-800 dark:text-amber-200",
                            "bg-emerald-100 dark:bg-emerald-900/30 text-emerald-800 dark:text-emerald-200",
                          ];
                          const colorIndex = Math.abs(tech.split('').reduce((acc, char) => acc + char.charCodeAt(0), 0)) % colors.length;

                          return (
                            <Badge
                              key={tech}
                              variant="secondary"
                              className={`${colors[colorIndex]} text-xs px-2 py-0.5 rounded-md`}
                            >
                              {tech}
                            </Badge>
                          );
                        })}
                      </div>
                    </div>

                    {/* View Project Button - Conditionally rendered based on projectUrl */}
                    {project.projectUrl ? (
                      <Button
                        variant="default"
                        className="group/button bg-teal hover:bg-teal/90 text-soft-white w-full"
                        asChild
                      >
                        <Link href={project.projectUrl}>
                          View Project
                          <ExternalLink className="ml-2 h-4 w-4 transition-transform group-hover/button:translate-x-1" />
                        </Link>
                      </Button>
                    ) : (
                      <Button
                        variant="outline"
                        className="group/button text-navy dark:text-warm-gray w-full cursor-not-allowed opacity-60"
                        disabled
                      >
                        Coming Soon
                      </Button>
                    )}
                  </CardContent>

                  <Meteors number={10} />
                </Card>
              </motion.div>
            ))}
          </div>
        )}

        {/* View All Projects Button */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ delay: 0.6 }}
          className="text-center mt-12"
        >
          <Button
            asChild
            className={cn(
              "group relative px-8 h-12 overflow-hidden",
              "bg-gradient-to-r from-blue-600 via-teal to-purple-500",
              "hover:from-blue-700 hover:via-teal/90 hover:to-purple-600",
              "text-soft-white font-medium shadow-lg border border-white/10",
              "transition-all duration-300 hover:shadow-teal/20 hover:shadow-xl",
              "hover:scale-[1.02]"
            )}
          >
            <Link href="/portfolio">
              <span className="relative z-10">View All Projects</span>
              <ArrowRight className="w-4 h-4 ml-2 inline-block group-hover:translate-x-1.5 transition-transform duration-300" />
              <span className="absolute inset-0 w-full h-full bg-gradient-to-r from-navy/0 via-teal/20 to-navy/0 -translate-x-full group-hover:translate-x-full transition-transform duration-1000"></span>
            </Link>
          </Button>
        </motion.div>
      </div>
    </section>
  );
};

export default PortfolioSection;
