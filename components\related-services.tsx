"use client";

import { motion } from "framer-motion";
import { servicesData } from "@/lib/data";
import { ServiceCard } from "@/components/ui/service-card";

interface RelatedServicesProps {
  currentServiceId: string;
  maxItems?: number;
}

const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1
    }
  }
};

export function RelatedServices({ currentServiceId, maxItems = 3 }: RelatedServicesProps) {
  // Get other services excluding the current one
  const relatedServices = servicesData
    .filter(service => service.id !== currentServiceId)
    .slice(0, maxItems);

  if (relatedServices.length === 0) {
    return null;
  }

  return (
    <section className="py-16 bg-gradient-to-b from-background to-muted/50">
      <div className="container mx-auto px-4">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          className="text-center mb-12"
        >
          <h2 className="text-3xl font-bold mb-4">Other Services</h2>
          <p className="text-muted-foreground">
            Explore our other services to see how we can help your business grow
          </p>
        </motion.div>

        <motion.div
          variants={containerVariants}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true }}
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 max-w-6xl mx-auto"
        >
          {relatedServices.map((service, index) => (
            <ServiceCard
              key={service.id}
              service={service}
              index={index}
              variant="compact"
            />
          ))}
        </motion.div>
      </div>
    </section>
  );
}
