"use client";
import { motion } from "framer-motion";
import {
  <PERSON>,
  <PERSON>Content,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  <PERSON>R<PERSON>,
  Code2,
  <PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON>hare,
} from "lucide-react";
import { Meteors } from "./ui/meteors";
import { GradientHeading } from "./ui/gradient-heading";
import { cn } from "@/lib/utils";
import Link from "next/link";

const services = [
  {
    id: 'web-development',
    icon: <Code2 className="h-8 w-8" />,
    title: "Web Development",
    description:
      "Custom web applications built with modern technologies and best practices.",
    color: "from-blue-500 to-cyan-500",
    bgColor: "bg-gradient-to-br from-blue-100 to-cyan-100",
    hoverBgColor: "bg-gradient-to-br from-blue-200 to-cyan-200",
    className: "md:col-span-2 md:row-span-2",
  },
  {
    id: 'ui-ux-design',
    icon: <Palette className="h-8 w-8" />,
    title: "UI/UX Design",
    description:
      "Beautiful, intuitive interfaces that enhance user experience and engagement.",
    color: "from-purple-500 to-pink-500",
    bgColor: "bg-gradient-to-br from-purple-100 to-pink-100",
    hoverBgColor: "bg-gradient-to-br from-purple-200 to-pink-200",
    className: "md:col-span-2 md:row-span-2",
  },
  {
    id: 'app-development',
    icon: <Rocket className="h-8 w-8" />,
    title: "App Development",
    description:
      "Native and cross-platform mobile applications for iOS and Android.",
    color: "from-red-500 to-rose-500",
    bgColor: "bg-gradient-to-br from-red-100 to-rose-100",
    hoverBgColor: "bg-gradient-to-br from-red-200 to-rose-200",
    className: "md:col-span-2",
  },
  {
    id: 'digital-marketing',
    icon: <LineChart className="h-8 w-8" />,
    title: "Digital Marketing",
    description:
      "Data-driven strategies to grow your online presence and reach.",
    color: "from-green-500 to-emerald-500",
    bgColor: "bg-gradient-to-br from-green-100 to-emerald-100",
    hoverBgColor: "bg-gradient-to-br from-green-200 to-emerald-200",
    className: "md:col-span-2",
  },
  {
    id: 'social-media-marketing',
    icon: <Share className="h-8 w-8" />,
    title: "Social Media Marketing",
    description:
      "Strategic social media management to build brand awareness and engagement.",
    color: "from-pink-500 to-rose-500",
    bgColor: "bg-gradient-to-br from-pink-100 to-rose-100",
    hoverBgColor: "bg-gradient-to-br from-pink-200 to-rose-200",
    className: "md:col-span-4",
  },
];

const cardVariants = {
  hidden: { opacity: 0, y: 20 },
  visible: (i: number) => ({
    opacity: 1,
    y: 0,
    transition: {
      delay: i * 0.1,
      duration: 0.5,
      ease: "easeOut",
    },
  }),
};

const ServiceSection = () => {
  return (
    <section id="services" className="py-12 relative overflow-hidden">
      <div className="container mx-auto px-4">
        {/* Section Header */}
        <div className="text-center mb-16">
          <motion.div
            initial={{ opacity: 0, y: -20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
          >
            <GradientHeading
              size="lg"
              gradient="blue"
              className="mb-4"
              as="h2"
            >
              Our Services
            </GradientHeading>
          </motion.div>
          <motion.p
            initial={{ opacity: 0, y: -20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ delay: 0.2 }}
            className="text-muted-foreground text-lg max-w-2xl mx-auto"
          >
            Comprehensive digital solutions to help your business thrive in the
            modern world
          </motion.p>
        </div>

        {/* Bento Grid */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4 max-w-5xl mx-auto">
          {services.map((service, index) => (
            <motion.div
              key={service.title}
              custom={index}
              initial="hidden"
              whileInView="visible"
              viewport={{ once: true }}
              variants={cardVariants}
              className={service.className}
            >
              <Card
                className={cn(
                  `group relative overflow-hidden border-2 hover:border-primary/50 transition-colors h-full transition-all duration-300`
                )}
              >
                <CardHeader>
                  {/* Gradient background for icon */}
                  <div
                    className={`w-16 h-16 rounded-lg bg-gradient-to-br ${service.color} p-4 mb-4`}
                  >
                    <div className="text-white">{service.icon}</div>
                  </div>
                  <CardTitle className="text-xl mb-2 dark:text-white">
                    {service.title}
                  </CardTitle>
                  <CardDescription className="dark:text-gray-300">
                    {service.description}
                  </CardDescription>
                </CardHeader>
                <CardContent className="z-10">
                  <Button
                    className={cn(
                      "w-full bg-gradient-to-r relative",
                      service.color,
                      "text-white border-0 hover:opacity-90",
                      "shadow-lg font-medium"
                    )}
                    style={{
                      textShadow: '0 1px 2px rgba(0, 0, 0, 0.5)'
                    }}
                    asChild
                  >
                    <Link
                      href={`/services/${service.id}`}
                      className="service-button-text flex items-center justify-center w-full h-full"
                    >
                      Learn More
                      <ArrowRight className="ml-2 h-4 w-4 transition-transform group-hover:translate-x-1" />
                    </Link>
                  </Button>
                </CardContent>

                <Meteors number={20} />
              </Card>
            </motion.div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default ServiceSection;