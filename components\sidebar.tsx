'use client'

import { useState, useEffect } from "react"
import Link from "next/link"
import { usePathname } from "next/navigation"
import { motion, AnimatePresence } from "framer-motion"
import {
  Home,
  Users,
  DollarSign,
  Wrench,
  Briefcase,
  BookOpen,
  Mail,
  ChevronLeft,
  ChevronRight,
  Menu
} from "lucide-react"
import { cn } from "@/lib/utils"
import { Button } from "@/components/ui/button"
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip"

const NavItems = [
  {
    title: "Home",
    href: "/",
    icon: Home,
    color: "text-blue-500",
    gradient: "from-blue-500 to-cyan-500"
  },
  {
    title: "About Us",
    href: "/about",
    icon: Users,
    color: "text-blue-500",
    gradient: "from-blue-500 to-cyan-500"
  },
  {
    title: "Services",
    href: "/services",
    icon: Wrench,
    color: "text-blue-500",
    gradient: "from-blue-500 to-indigo-500"
  },
  {
    title: "Portfolio",
    href: "/portfolio",
    icon: Briefcase,
    color: "text-green-500",
    gradient: "from-green-500 to-emerald-500"
  },
  {
    title: "Pricing",
    href: "/pricing",
    icon: DollarSign,
    color: "text-green-500",
    gradient: "from-green-500 to-emerald-500"
  },
  {
    title: "Blogs",
    href: "/blogs",
    icon: BookOpen,
    color: "text-purple-500",
    gradient: "from-purple-500 to-indigo-500"
  },
  {
    title: "Contact Us",
    href: "/contact",
    icon: Mail,
    color: "text-red-500",
    gradient: "from-red-500 to-pink-500"
  },
]

const Sidebar = () => {
  const [isOpen, setIsOpen] = useState(true)
  const [isCollapsed, setIsCollapsed] = useState(false)
  const pathname = usePathname()
  
  // Handle window resize
  useEffect(() => {
    const handleResize = () => {
      if (window.innerWidth < 1024) {
        setIsOpen(false)
      } else {
        setIsOpen(true)
      }
    }

    window.addEventListener('resize', handleResize)
    handleResize()

    return () => window.removeEventListener('resize', handleResize)
  }, [])

  // Toggle button that appears on mobile
  const ToggleButton = () => (
    <Button
      variant="outline"
      size="icon"
      onClick={() => setIsOpen(!isOpen)}
      className={cn(
        "fixed top-4 left-4 z-50",
        "lg:hidden",
        "bg-background/80 backdrop-blur-sm",
        "border-border/40"
      )}
    >
      <Menu className="h-4 w-4" />
    </Button>
  )

  return (
    <>
      <ToggleButton />
      
      <AnimatePresence mode="wait">
        {isOpen && (
          <>
            {/* Backdrop for mobile */}
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              onClick={() => setIsOpen(false)}
              className="fixed inset-0 bg-background/80 backdrop-blur-sm z-40 lg:hidden"
            />

            {/* Sidebar */}
            <motion.div
              initial={{ 
                x: -300,
                width: isCollapsed ? 80 : 256
              }}
              animate={{ 
                x: 0,
                width: isCollapsed ? 80 : 256
              }}
              exit={{ 
                x: -300
              }}
              transition={{
                type: "spring",
                stiffness: 400,
                damping: 40
              }}
              className={cn(
                "fixed lg:relative",
                "flex flex-col h-screen",
                "bg-white dark:bg-gray-950",
                "border-r border-border/40",
                "shadow-lg shadow-black/5",
                "z-50"
              )}
            >
              {/* Logo Section */}
              <div className={cn(
                "flex items-center h-20 px-6",
                "border-b border-border/40",
                isCollapsed ? "justify-center" : "justify-between"
              )}>
                {!isCollapsed && (
                  <motion.div
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    exit={{ opacity: 0 }}
                    className="font-bold text-xl bg-gradient-to-r from-blue-600 to-cyan-600 bg-clip-text text-transparent"
                  >
                    Flerid Tech
                  </motion.div>
                )}
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={() => setIsCollapsed(!isCollapsed)}
                  className={cn(
                    "shrink-0",
                    "hidden lg:flex", // Only show on desktop
                    "hover:bg-muted/50"
                  )}
                >
                  {isCollapsed ? (
                    <ChevronRight className="h-4 w-4" />
                  ) : (
                    <ChevronLeft className="h-4 w-4" />
                  )}
                </Button>
              </div>

              {/* Navigation */}
              <nav className="flex-1 overflow-y-auto p-4">
                <TooltipProvider delayDuration={0}>
                  <ul className="space-y-2">
                    {NavItems.map((item) => {
                      const Icon = item.icon
                      const isActive = pathname === item.href

                      return (
                        <li key={item.href}>
                          <Tooltip>
                            <TooltipTrigger asChild>
                              <Link
                                href={item.href}
                                onClick={() => {
                                  // Close sidebar on mobile when clicking a link
                                  if (window.innerWidth < 1024) {
                                    setIsOpen(false)
                                  }
                                }}
                                className={cn(
                                  "flex items-center gap-4 px-3 py-2 rounded-lg",
                                  "transition-all duration-200",
                                  "group relative overflow-hidden",
                                  isActive
                                    ? "text-white"
                                    : "hover:text-white",
                                  isCollapsed && "justify-center"
                                )}
                              >
                                {/* Background gradient */}
                                <div className={cn(
                                  "absolute inset-0 opacity-0 bg-gradient-to-r transition-opacity",
                                  item.gradient,
                                  isActive ? "opacity-100" : "group-hover:opacity-100"
                                )} />

                                {/* Icon with gradient on hover */}
                                <div className={cn(
                                  "relative z-10 p-2 rounded-md transition-colors",
                                  "bg-background/50 backdrop-blur-sm",
                                  isActive ? item.color : "text-muted-foreground group-hover:text-white"
                                )}>
                                  <Icon className="h-4 w-4" />
                                </div>

                                {/* Title with animation */}
                                {!isCollapsed && (
                                  <motion.span
                                    initial={{ opacity: 0, x: -10 }}
                                    animate={{ opacity: 1, x: 0 }}
                                    exit={{ opacity: 0, x: -10 }}
                                    className="relative z-10 font-medium"
                                  >
                                    {item.title}
                                  </motion.span>
                                )}
                              </Link>
                            </TooltipTrigger>
                            {isCollapsed && (
                              <TooltipContent side="right">
                                {item.title}
                              </TooltipContent>
                            )}
                          </Tooltip>
                        </li>
                      )
                    })}
                  </ul>
                </TooltipProvider>
              </nav>

              {/* Footer */}
              <div className={cn(
                "p-4 border-t border-border/40",
                "text-xs text-center text-muted-foreground",
                isCollapsed ? "px-2" : "px-6"
              )}>
                {!isCollapsed && "© 2024 Flerid Technologies"}
              </div>
            </motion.div>
          </>
        )}
      </AnimatePresence>
    </>
  )
}

export default Sidebar