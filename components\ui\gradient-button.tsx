import * as React from "react";
import { Slot } from "@radix-ui/react-slot";
import { cva, type VariantProps } from "class-variance-authority";
import { cn } from "@/lib/utils";

const gradientButtonVariants = cva(
  "inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all duration-300 focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0 text-white border-0 shadow-lg hover:opacity-90",
  {
    variants: {
      gradient: {
        primary: "bg-gradient-to-r from-primary to-blue-600",
        blue: "bg-gradient-to-r from-blue-500 to-cyan-500",
        green: "bg-gradient-to-r from-green-500 to-emerald-500",
        purple: "bg-gradient-to-r from-violet-500 to-purple-500",
        pink: "bg-gradient-to-r from-pink-500 to-rose-500",
        orange: "bg-gradient-to-r from-orange-500 to-red-500",
      },
      size: {
        default: "h-9 px-4 py-2",
        sm: "h-8 rounded-md px-3 text-xs",
        lg: "h-10 rounded-md px-8",
        xl: "h-12 rounded-md px-10 text-base",
        icon: "h-9 w-9",
      },
    },
    defaultVariants: {
      gradient: "primary",
      size: "default",
    },
  }
);

export interface GradientButtonProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement>,
    VariantProps<typeof gradientButtonVariants> {
  asChild?: boolean;
}

const GradientButton = React.forwardRef<HTMLButtonElement, GradientButtonProps>(
  ({ className, gradient, size, asChild = false, style, ...props }, ref) => {
    const Comp = asChild ? Slot : "button";
    return (
      <Comp
        className={cn(gradientButtonVariants({ gradient, size, className }))}
        ref={ref}
        style={{
          textShadow: '0 1px 2px rgba(0, 0, 0, 0.5)',
          ...style,
        }}
        {...props}
      />
    );
  }
);
GradientButton.displayName = "GradientButton";

export { GradientButton, gradientButtonVariants };
