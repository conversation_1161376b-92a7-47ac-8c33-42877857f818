import { cn } from "@/lib/utils";
import { ReactNode } from "react";

interface GradientHeadingProps {
  children: ReactNode;
  className?: string;
  size?: "sm" | "md" | "lg" | "xl";
  gradient?: "primary" | "blue" | "green" | "purple" | "pink" | "orange";
  as?: "h1" | "h2" | "h3" | "h4" | "h5" | "h6";
}

const sizeClasses = {
  sm: "text-2xl md:text-3xl",
  md: "text-3xl md:text-4xl",
  lg: "text-4xl md:text-5xl",
  xl: "text-4xl md:text-6xl",
};

const gradientClasses = {
  primary: "from-blue-600 via-indigo-600 to-purple-600",
  blue: "from-blue-600 via-blue-500 to-cyan-400",
  green: "from-green-600 via-emerald-500 to-teal-400",
  purple: "from-purple-600 via-violet-500 to-indigo-400",
  pink: "from-pink-600 via-rose-500 to-orange-400",
  orange: "from-orange-600 via-red-500 to-pink-400",
};

export function GradientHeading({
  children,
  className,
  size = "lg",
  gradient = "primary",
  as: Component = "h1",
}: GradientHeadingProps) {
  return (
    <Component
      className={cn(
        "font-bold bg-gradient-to-r bg-clip-text text-transparent",
        "bg-size-200 animate-gradient-x",
        sizeClasses[size],
        gradientClasses[gradient],
        className
      )}
      style={{
        backgroundSize: "200% 200%",
        WebkitBackgroundClip: "text",
        WebkitTextFillColor: "transparent",
        lineHeight: "1.3",
      }}
    >
      {children}
    </Component>
  );
}
