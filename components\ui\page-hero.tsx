"use client";

import { motion } from "framer-motion";
import { ReactNode } from "react";
import { cn } from "@/lib/utils";
import { GradientHeading } from "./gradient-heading";
import { Badge } from "./badge";

interface PageHeroProps {
  title: string;
  description?: string;
  badges?: Array<{
    text: string;
    variant?: "default" | "secondary" | "outline";
  }>;
  children?: ReactNode;
  className?: string;
  titleGradient?: "primary" | "blue" | "green" | "purple" | "pink" | "orange";
  backgroundGradient?: boolean;
}

export function PageHero({
  title,
  description,
  badges,
  children,
  className,
  titleGradient = "primary",
  backgroundGradient = true,
}: PageHeroProps) {
  return (
    <section
      className={cn(
        "relative py-20",
        backgroundGradient && "bg-gradient-to-br from-background via-muted/50 to-background",
        className
      )}
    >
      <div className="container mx-auto px-4">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="text-center max-w-4xl mx-auto"
        >
          <GradientHeading
            size="xl"
            gradient={titleGradient}
            className="mb-6"
          >
            {title}
          </GradientHeading>
          
          {description && (
            <p className="text-xl text-muted-foreground mb-8 leading-relaxed">
              {description}
            </p>
          )}
          
          {badges && badges.length > 0 && (
            <div className="flex flex-wrap justify-center gap-4 mb-8">
              {badges.map((badge, index) => (
                <Badge
                  key={index}
                  variant={badge.variant || "outline"}
                  className="px-4 py-2"
                >
                  {badge.text}
                </Badge>
              ))}
            </div>
          )}
          
          {children}
        </motion.div>
      </div>
    </section>
  );
}
