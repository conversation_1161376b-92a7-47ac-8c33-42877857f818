"use client";

import { cn } from "@/lib/utils";

interface PlaceholderFallbackProps {
  className?: string;
}

export function PlaceholderFallback({ className }: PlaceholderFallbackProps) {
  return (
    <div className={cn("flex items-center justify-center bg-muted/30 w-full h-full", className)}>
      <svg
        xmlns="http://www.w3.org/2000/svg"
        width="48"
        height="48"
        viewBox="0 0 24 24"
        fill="none"
        stroke="currentColor"
        strokeWidth="1"
        strokeLinecap="round"
        strokeLinejoin="round"
        className="text-muted-foreground/50"
      >
        <rect width="18" height="18" x="3" y="3" rx="2" ry="2" />
        <circle cx="9" cy="9" r="2" />
        <path d="m21 15-3.086-3.086a2 2 0 0 0-2.828 0L6 21" />
      </svg>
    </div>
  );
}
