"use client";

import { useState, useEffect } from "react";
import Image, { ImageProps } from "next/image";
import { cn } from "@/lib/utils";
import { PlaceholderFallback } from "./placeholder-image-fallback";

interface PlaceholderImageProps extends Omit<ImageProps, "src" | "alt"> {
  src: string;
  alt: string;
  fallbackSrc?: string;
  timeout?: number; // Timeout in milliseconds
}

export function PlaceholderImage({
  src,
  alt,
  fallbackSrc = "/placeholder-project.svg",
  timeout = 5000, // 5 second timeout by default
  className,
  ...props
}: PlaceholderImageProps) {
  const [imgSrc, setImgSrc] = useState(src);
  const [hasError, setHasError] = useState(false);
  const [hasFallbackError, setHasFallbackError] = useState(false);
  const [isLoading, setIsLoading] = useState(true);

  // If the source is empty or undefined, show the fallback immediately
  if (!src || src === '') {
    return <PlaceholderFallback className={className} />;
  }

  // Set up timeout for slow-loading images
  useEffect(() => {
    if (!isLoading) return;

    const timeoutId = setTimeout(() => {
      if (isLoading) {
        console.warn(`Image load timeout for: ${imgSrc}`);
        if (fallbackSrc && fallbackSrc !== imgSrc && !hasError) {
          setImgSrc(fallbackSrc);
          setHasError(true);
        } else {
          setHasError(true);
          setHasFallbackError(true);
        }
        setIsLoading(false);
      }
    }, timeout);

    return () => clearTimeout(timeoutId);
  }, [imgSrc, isLoading, hasError, fallbackSrc, timeout]);

  // If both original and fallback failed, show placeholder
  if (hasError && hasFallbackError) {
    return <PlaceholderFallback className={className} />;
  }

  return (
    <>
      <Image
        {...props}
        src={imgSrc}
        alt={alt}
        className={cn(className, hasError ? "bg-muted" : "")}
        onLoad={() => {
          setIsLoading(false);
        }}
        onError={() => {
          setIsLoading(false);
          // Only log error for non-placeholder images to reduce console noise
          if (!imgSrc.includes('placeholder')) {
            console.warn(`Image load failed for: ${imgSrc}`);
          }

          if (fallbackSrc && fallbackSrc !== imgSrc && !hasError) {
            setImgSrc(fallbackSrc);
            setHasError(true);
            setIsLoading(true); // Try loading the fallback
          } else if (hasError && !hasFallbackError) {
            setHasFallbackError(true);
          } else {
            setHasError(true);
          }
        }}
      />
      {hasError && hasFallbackError && <PlaceholderFallback className={cn("absolute inset-0", className)} />}
    </>
  );
}
