import { cn } from "@/lib/utils";
import { ReactNode } from "react";

interface SectionProps {
  children: ReactNode;
  className?: string;
  background?: "default" | "muted" | "gradient";
  padding?: "sm" | "md" | "lg" | "xl";
  container?: boolean;
}

const paddingClasses = {
  sm: "py-8",
  md: "py-12",
  lg: "py-16",
  xl: "py-20",
};

const backgroundClasses = {
  default: "",
  muted: "bg-muted/50",
  gradient: "bg-gradient-to-b from-muted/50 to-background",
};

export function Section({
  children,
  className,
  background = "default",
  padding = "lg",
  container = true,
}: SectionProps) {
  return (
    <section
      className={cn(
        paddingClasses[padding],
        backgroundClasses[background],
        className
      )}
    >
      {container ? (
        <div className="container mx-auto px-4">
          {children}
        </div>
      ) : (
        children
      )}
    </section>
  );
}
