"use client";

import Link from "next/link";
import { ChevronRight, Home } from "lucide-react";

interface ServiceBreadcrumbProps {
  serviceTitle: string;
  className?: string;
}

export function ServiceBreadcrumb({ serviceTitle, className = "" }: ServiceBreadcrumbProps) {
  return (
    <nav className={`bg-muted/30 border-b ${className}`}>
      <div className="container mx-auto px-4 py-3">
        <div className="flex items-center space-x-2 text-sm">
          <Link 
            href="/" 
            className="text-muted-foreground hover:text-foreground transition-colors flex items-center gap-1"
          >
            <Home className="w-4 h-4" />
            Home
          </Link>
          <ChevronRight className="w-4 h-4 text-muted-foreground" />
          <Link 
            href="/services" 
            className="text-muted-foreground hover:text-foreground transition-colors"
          >
            Services
          </Link>
          <ChevronRight className="w-4 h-4 text-muted-foreground" />
          <span className="text-foreground font-medium">{serviceTitle}</span>
        </div>
      </div>
    </nav>
  );
}
