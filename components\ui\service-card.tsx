"use client";

import Link from "next/link";
import { motion, AnimatePresence } from "framer-motion";
import { ArrowRight, ChevronDown } from "lucide-react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { ServiceIcon } from "@/components/ui/service-icon";
import { cn } from "@/lib/utils";
import React, { useState } from "react";

interface ServiceCardProps {
  service: {
    id: string;
    icon: string | React.ReactNode;
    title: string;
    description: string;
    color: string;
    features: Array<{ title: string; description: string }>;
    technologies: string[];
  };
  index?: number;
  variant?: "default" | "compact";
}



const cardVariants = {
  hidden: { opacity: 0, y: 20 },
  visible: { 
    opacity: 1, 
    y: 0,
    transition: { duration: 0.5 }
  }
};

export function ServiceCard({ service, index = 0, variant = "default" }: ServiceCardProps) {
  const isCompact = variant === "compact";
  const [isExpanded, setIsExpanded] = useState(false);

  const displayLimit = isCompact ? 3 : 4;
  const hasMoreTechnologies = service.technologies.length > displayLimit;

  return (
    <motion.div
      variants={cardVariants}
      initial="hidden"
      animate="visible"
      transition={{ delay: index * 0.1 }}
      className="group h-full"
      style={{ position: 'relative', zIndex: 'auto' }}
    >
      <Card className="h-full hover:shadow-xl transition-all duration-300 border-border/50 hover:border-primary/20 overflow-hidden relative isolate">
        {/* Gradient Background - Fixed z-index and pointer events */}
        <div className={cn(
          "absolute inset-0 opacity-0 group-hover:opacity-5 transition-opacity duration-300 pointer-events-none",
          "bg-gradient-to-br",
          service.color
        )} style={{ zIndex: 1 }} />

        <CardHeader className="relative" style={{ zIndex: 2 }}>
          <div className={cn(
            "rounded-2xl flex items-center justify-center mb-4",
            "bg-gradient-to-br",
            service.color,
            "text-white shadow-lg",
            isCompact ? "w-12 h-12" : "w-16 h-16"
          )}>
            <ServiceIcon iconName={service.icon as string} />
          </div>
          <CardTitle className={cn(
            "font-bold group-hover:text-primary transition-colors",
            isCompact ? "text-lg" : "text-xl"
          )}>
            {service.title}
          </CardTitle>
          <CardDescription className="text-muted-foreground leading-relaxed">
            {service.description}
          </CardDescription>
        </CardHeader>
        
        <CardContent className="relative" style={{ zIndex: 2 }}>
          <div className="space-y-4">
            {/* Key Features Preview */}
            {!isCompact && (
              <div>
                <h4 className="font-semibold mb-2 text-sm">Key Features:</h4>
                <ul className="space-y-1">
                  {service.features.slice(0, 3).map((feature, idx) => (
                    <li key={idx} className="text-sm text-muted-foreground flex items-center gap-2">
                      <div className="w-1.5 h-1.5 rounded-full bg-primary" />
                      {feature.title}
                    </li>
                  ))}
                </ul>
              </div>
            )}
            
            {/* Technologies Preview */}
            <div>
              <h4 className="font-semibold mb-2 text-sm">Technologies:</h4>
              <div className="flex flex-wrap gap-1">
                {/* Always visible technologies */}
                {service.technologies.slice(0, displayLimit).map((tech, idx) => (
                  <Badge key={idx} variant="secondary" className="text-xs">
                    {tech}
                  </Badge>
                ))}

                {/* Expandable hidden technologies */}
                <AnimatePresence>
                  {isExpanded && (
                    <motion.div
                      initial={{ opacity: 0, scale: 0.95 }}
                      animate={{ opacity: 1, scale: 1 }}
                      exit={{ opacity: 0, scale: 0.95 }}
                      transition={{ duration: 0.2, ease: "easeInOut" }}
                      className="flex flex-wrap gap-1 w-full"
                    >
                      {service.technologies.slice(displayLimit).map((tech, idx) => (
                        <Badge key={`hidden-${idx}`} variant="secondary" className="text-xs">
                          {tech}
                        </Badge>
                      ))}
                    </motion.div>
                  )}
                </AnimatePresence>

                {/* Toggle button - only show when not expanded */}
                {hasMoreTechnologies && !isExpanded && (
                  <Badge
                    variant="outline"
                    className="text-xs cursor-pointer hover:bg-accent transition-colors flex items-center gap-1"
                    onClick={() => setIsExpanded(true)}
                  >
                    many more
                    <ChevronDown className="w-3 h-3" />
                  </Badge>
                )}
              </div>
            </div>
          </div>
          
          <Button
            asChild
            className={cn(
              "w-full mt-6 bg-gradient-to-r relative",
              service.color,
              "text-white border-0 hover:opacity-90",
              "shadow-lg font-medium"
            )}
            size="default"
            style={{
              zIndex: 10,
              textShadow: '0 1px 2px rgba(0, 0, 0, 0.5)'
            }}
          >
            <Link
              href={`/services/${service.id}`}
              className="relative z-10 flex items-center justify-center w-full h-full service-button-text"
            >
              Learn More
              <ArrowRight className="ml-2 w-4 h-4" />
            </Link>
          </Button>
        </CardContent>
      </Card>
    </motion.div>
  );
}
