"use client";

import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, Share } from "lucide-react";
import { cn } from "@/lib/utils";

interface ServiceIconProps {
  iconName: string;
  className?: string;
}

export function ServiceIcon({ iconName, className = "w-8 h-8" }: ServiceIconProps) {
  switch (iconName) {
    case "Code2":
      return <Code2 className={cn(className)} />;
    case "Palette":
      return <Palette className={cn(className)} />;
    case "Rocket":
      return <Rocket className={cn(className)} />;
    case "LineChart":
      return <LineChart className={cn(className)} />;
    case "Share":
      return <Share className={cn(className)} />;
    default:
      return <Code2 className={cn(className)} />; // Default fallback
  }
}
