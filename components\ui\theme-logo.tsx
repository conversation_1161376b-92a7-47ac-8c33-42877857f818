"use client";

import { useState, useEffect } from "react";
import { useTheme } from "next-themes";
import Image from "next/image";

interface ThemeLogoProps {
  width?: number;
  height?: number;
  className?: string;
}

export const ThemeLogo = ({ 
  width = 24, 
  height = 24, 
  className = "" 
}: ThemeLogoProps) => {
  const { resolvedTheme } = useTheme();
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  // Show a placeholder during SSR to avoid hydration mismatch
  if (!mounted) {
    return (
      <div 
        className={`bg-gray-300 rounded animate-pulse ${className}`}
        style={{ width, height }}
      />
    );
  }

  // Use logo-light.png for dark theme, logo-dark.png for light theme
  const logoSrc = resolvedTheme === 'dark' ? '/logo-light.png' : '/logo-dark.png';

  return (
    <Image 
      src={logoSrc} 
      alt="Flerid Technologies" 
      width={width} 
      height={height}
      className={`transition-opacity duration-200 ${className}`}
    />
  );
};
