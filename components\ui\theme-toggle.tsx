"use client";

import { useState, useEffect } from "react";
import { useTheme } from "next-themes";
import { Button } from "@/components/ui/button";
import { Moon, Sun } from "lucide-react";

interface ThemeToggleProps {
  className?: string;
  iconClassName?: string;
}

export const ThemeToggle = ({ 
  className = "", 
  iconClassName = "h-5 w-5" 
}: ThemeToggleProps) => {
  const { setTheme, resolvedTheme } = useTheme();
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  // Show a placeholder during SSR to avoid hydration mismatch
  if (!mounted) {
    return (
      <Button variant="ghost" size="icon" disabled className={className}>
        <div className={iconClassName} />
      </Button>
    );
  }

  const toggleTheme = () => {
    setTheme(resolvedTheme === "light" ? "dark" : "light");
  };

  return (
    <Button 
      variant="ghost" 
      size="icon" 
      onClick={toggleTheme}
      className={className}
    >
      {resolvedTheme === "light" ? (
        <Moon className={`text-blue-600 ${iconClassName}`} />
      ) : (
        <Sun className={`text-blue-300 ${iconClassName}`} />
      )}
    </Button>
  );
};
