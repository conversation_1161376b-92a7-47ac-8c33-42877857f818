"use client";

import React, { useEffect, useState } from 'react';
import { motion } from 'framer-motion';

interface WaterDrop {
  id: number;
  delay: number;
  duration: number;
  size: number;
  startX: number;
  startY: number;
  color: string;
  flowDirection: number;
  waveAmplitude: number;
  fallSpeed: number;
}

interface WaterRipple {
  id: number;
  delay: number;
  duration: number;
  size: number;
  startX: number;
  startY: number;
  color: string;
  rippleIntensity: number;
}



interface WaterBubble {
  id: number;
  delay: number;
  duration: number;
  size: number;
  startX: number;
  startY: number;
  color: string;
  riseSpeed: number;
  wobbleIntensity: number;
}

const WateryParticles: React.FC = () => {
  const [prefersReducedMotion, setPrefersReducedMotion] = useState(false);
  const [waterDrops, setWaterDrops] = useState<WaterDrop[]>([]);
  const [waterRipples, setWaterRipples] = useState<WaterRipple[]>([]);

  const [waterBubbles, setWaterBubbles] = useState<WaterBubble[]>([]);
  const [isClient, setIsClient] = useState(false);

  useEffect(() => {
    setIsClient(true);

    // Check for reduced motion preference
    const checkReducedMotion = () => {
      setPrefersReducedMotion(window.matchMedia('(prefers-reduced-motion: reduce)').matches);
    };

    checkReducedMotion();

    // Generate water drops - falling like real raindrops
    const generatedWaterDrops = Array.from({ length: 25 }, (_, index) => {
      return {
        id: index,
        delay: Math.random() * 15,
        duration: 8 + Math.random() * 12, // 8-20 seconds
        size: Math.random() * 8 + 3, // 3-11px
        startX: Math.random() * 120 - 10, // -10% to 110%
        startY: -10 - Math.random() * 20, // Start above viewport
        color: Math.random() > 0.5 ? 'rgba(59, 130, 246, 0.25)' : 'rgba(6, 182, 212, 0.25)',
        flowDirection: Math.random() * 30 - 15, // -15 to 15 degrees
        waveAmplitude: Math.random() * 20 + 10, // 10-30px wave movement
        fallSpeed: Math.random() * 0.5 + 0.5, // 0.5-1.0 fall speed multiplier
      };
    });

    // Generate water ripples - expanding circles like water surface
    const generatedWaterRipples = Array.from({ length: 15 }, (_, index) => {
      return {
        id: index,
        delay: Math.random() * 20,
        duration: 6 + Math.random() * 8, // 6-14 seconds
        size: Math.random() * 40 + 20, // 20-60px initial size
        startX: Math.random() * 100,
        startY: Math.random() * 100,
        color: Math.random() > 0.5 ? 'rgba(59, 130, 246, 0.08)' : 'rgba(6, 182, 212, 0.08)',
        rippleIntensity: Math.random() * 0.5 + 0.3, // 0.3-0.8 intensity
      };
    });



    // Generate water bubbles - rising from bottom like underwater bubbles
    const generatedWaterBubbles = Array.from({ length: 12 }, (_, index) => {
      return {
        id: index,
        delay: Math.random() * 18,
        duration: 12 + Math.random() * 15, // 12-27 seconds
        size: Math.random() * 12 + 4, // 4-16px
        startX: Math.random() * 100,
        startY: 110 + Math.random() * 20, // Start below viewport
        color: Math.random() > 0.5 ? 'rgba(59, 130, 246, 0.18)' : 'rgba(6, 182, 212, 0.18)',
        riseSpeed: Math.random() * 0.3 + 0.4, // 0.4-0.7 rise speed
        wobbleIntensity: Math.random() * 15 + 5, // 5-20px wobble
      };
    });

    setWaterDrops(generatedWaterDrops);
    setWaterRipples(generatedWaterRipples);
    setWaterBubbles(generatedWaterBubbles);
  }, []);

  if (!isClient || prefersReducedMotion) {
    return null; // Don't render particles for users who prefer reduced motion or during SSR
  }

  return (
    <div
      className="fixed inset-0 pointer-events-none overflow-hidden"
      style={{
        zIndex: 5,
        left: 0,
        right: 0,
        top: 0,
        bottom: 0,
        width: '100vw',
        height: '100vh',
        minHeight: '100vh'
      }}
      aria-hidden="true"
    >
      {/* Water drops - falling like rain */}
      {waterDrops.map((drop) => (
        <motion.div
          key={`water-drop-${drop.id}`}
          className="absolute rounded-full"
          style={{
            width: drop.size,
            height: drop.size * 1.2, // Slightly elongated like real water drops
            backgroundColor: drop.color,
            filter: 'blur(1px)',
            left: `${drop.startX}%`,
            top: `${drop.startY}%`,
            willChange: 'transform, opacity',
            borderRadius: '50% 50% 50% 50% / 60% 60% 40% 40%', // Teardrop shape
          }}
          animate={{
            y: [0, 120 * drop.fallSpeed], // Fall down the screen
            x: [0, drop.waveAmplitude * 0.5, -drop.waveAmplitude * 0.3, drop.flowDirection], // Wavy falling motion
            opacity: [0, 0.8, 0.6, 0],
            scale: [0.8, 1, 0.9, 0.6],
          }}
          transition={{
            duration: drop.duration,
            repeat: Infinity,
            ease: "easeOut",
            delay: drop.delay,
          }}
        />
      ))}

      {/* Water ripples - expanding circles */}
      {waterRipples.map((ripple) => (
        <motion.div
          key={`water-ripple-${ripple.id}`}
          className="absolute rounded-full border-2"
          style={{
            width: ripple.size,
            height: ripple.size,
            borderColor: ripple.color,
            backgroundColor: 'transparent',
            filter: 'blur(0.5px)',
            left: `${ripple.startX}%`,
            top: `${ripple.startY}%`,
            willChange: 'transform, opacity',
          }}
          animate={{
            scale: [0.2, 2.5, 3.5], // Expanding ripple effect
            opacity: [0, ripple.rippleIntensity, 0],
            borderWidth: [4, 2, 0], // Border fades as it expands
          }}
          transition={{
            duration: ripple.duration,
            repeat: Infinity,
            ease: "easeOut",
            delay: ripple.delay,
          }}
        />
      ))}



      {/* Water bubbles - rising from bottom */}
      {waterBubbles.map((bubble) => (
        <motion.div
          key={`water-bubble-${bubble.id}`}
          className="absolute rounded-full"
          style={{
            width: bubble.size,
            height: bubble.size,
            backgroundColor: bubble.color,
            filter: 'blur(1.5px)',
            left: `${bubble.startX}%`,
            top: `${bubble.startY}%`,
            willChange: 'transform, opacity',
            border: `1px solid ${bubble.color.replace('0.18', '0.3')}`,
          }}
          animate={{
            y: [0, -130 * bubble.riseSpeed], // Rise up like underwater bubbles
            x: [0, bubble.wobbleIntensity * 0.5, -bubble.wobbleIntensity * 0.3, bubble.wobbleIntensity * 0.2, 0], // Wobbling motion as they rise
            scale: [0.6, 1, 1.1, 0.8, 0.4], // Size changes as they rise
            opacity: [0, 0.7, 0.9, 0.4, 0],
          }}
          transition={{
            duration: bubble.duration,
            repeat: Infinity,
            ease: "easeOut",
            delay: bubble.delay,
          }}
        />
      ))}
    </div>
  );
};

export default WateryParticles;
