"use client";

import Link from "next/link";
import { motion } from "framer-motion";
import { ArrowRight } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { GradientButton } from "@/components/ui/gradient-button";
import { GradientHeading } from "@/components/ui/gradient-heading";
import { Section } from "@/components/ui/section";
import { cn } from "@/lib/utils";

export default function WhoWeAre() {
  return (
    <Section background="default" padding="xl">
      <div className="max-w-4xl mx-auto">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.5 }}
          className="text-center mb-12"
        >
          <GradientHeading
            size="lg"
            gradient="blue"
            className="mb-6"
            as="h2"
          >
            Who We Are
          </GradientHeading>
          <p className="text-lg text-muted-foreground leading-relaxed">
            Flerid is a dynamic web and app development, digital marketing
            company. Our savvy team offers a strategic approach, leveraging
            branding and marketing, and employs research-driven methods to
            propel your business towards unprecedented success and goal
            attainment.
          </p>
        </motion.div>

          {/* Mission & Vision */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.5, delay: 0.2 }}
            className="grid md:grid-cols-2 gap-8 mb-12"
          >
            <div className="p-6 rounded-lg border bg-card hover:shadow-lg transition-all duration-300 group">
              <div className="w-12 h-12 rounded-lg bg-gradient-to-br from-blue-500 to-cyan-500 flex items-center justify-center mb-4">
                <span className="text-white font-bold text-lg">M</span>
              </div>
              <GradientHeading
                size="sm"
                gradient="blue"
                className="mb-4"
                as="h3"
              >
                Our Mission
              </GradientHeading>
              <p className="text-muted-foreground leading-relaxed">
                To deliver innovative digital solutions that empower businesses
                to thrive in the modern world. We strive to create meaningful
                impact through technology and creativity.
              </p>
            </div>
            <div className="p-6 rounded-lg border bg-card hover:shadow-lg transition-all duration-300 group">
              <div className="w-12 h-12 rounded-lg bg-gradient-to-br from-green-500 to-emerald-500 flex items-center justify-center mb-4">
                <span className="text-white font-bold text-lg">V</span>
              </div>
              <GradientHeading
                size="sm"
                gradient="green"
                className="mb-4"
                as="h3"
              >
                Our Vision
              </GradientHeading>
              <p className="text-muted-foreground leading-relaxed">
                To be the leading force in digital transformation, setting new
                standards in technology and creativity. We aim to shape the
                future of digital experiences.
              </p>
            </div>
          </motion.div>

          {/* Values */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.5, delay: 0.4 }}
            className="text-center"
          >
            <GradientHeading
              size="md"
              gradient="purple"
              className="mb-8"
              as="h3"
            >
              Our Core Values
            </GradientHeading>
            <div className="grid sm:grid-cols-2 lg:grid-cols-4 gap-6">
              {[
                {
                  title: "Innovation",
                  desc: "Pushing boundaries in technology",
                },
                { title: "Excellence", desc: "Delivering outstanding quality" },
                {
                  title: "Integrity",
                  desc: "Building trust through transparency",
                },
                {
                  title: "Collaboration",
                  desc: "Working together for success",
                },
              ].map((value, index) => (
                <div
                  key={value.title}
                  className="p-4 rounded-lg border bg-card"
                >
                  <h4 className="font-semibold mb-2">{value.title}</h4>
                  <p className="text-sm text-muted-foreground">{value.desc}</p>
                </div>
              ))}
            </div>
          </motion.div>

          {/* CTA */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.5, delay: 0.6 }}
            className="text-center mt-12"
          >
            <GradientButton
              size="xl"
              gradient="blue"
              asChild
            >
              <Link
                href="/about"
                className="service-button-text flex items-center"
              >
                <span className="relative z-10">Learn More About Us</span>
                <ArrowRight className="w-4 h-4 ml-2 group-hover:translate-x-1 transition-transform" />
              </Link>
            </GradientButton>
          </motion.div>
        </div>
    </Section>
  );
}
