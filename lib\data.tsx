import { Code2, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, Share } from 'lucide-react';

// Service name to slug mapping for footer navigation
export const serviceSlugMap: Record<string, string> = {
  'Web Design & Development': 'web-development',
  'Social Media Marketing': 'social-media-marketing',
  'SEO': 'seo',
  'Mobile App Development': 'app-development',
  'Software Development': 'software-development'
};

// Helper function to get service URL from name
export const getServiceUrl = (serviceName: string): string => {
  const slug = serviceSlugMap[serviceName];
  return slug ? `/services/${slug}` : '/services';
};

export const servicesData = [
  {
    id: 'web-development',
    icon: 'Code2',
    title: 'Web Design & Development',
    description: 'Custom web applications built with modern technologies and best practices.',
    color: 'from-blue-500 to-cyan-500',
    bgColor: 'bg-gradient-to-br from-blue-100 to-cyan-100',
    hoverBgColor: 'bg-gradient-to-br from-blue-200 to-cyan-200',
    heroImage: 'https://images.unsplash.com/photo-1498050108023-c5249f4df085?q=80&w=2072&auto=format&fit=crop',
    features: [
      {
        title: 'Custom Web Applications',
        description: 'Tailored solutions that perfectly match your business requirements and goals.'
      },
      {
        title: 'Responsive Design',
        description: 'Websites that look and function beautifully across all devices and screen sizes.'
      },
      {
        title: 'Performance Optimization',
        description: 'Lightning-fast loading times and optimal user experience.'
      },
      {
        title: 'Modern Technologies',
        description: 'Built with the latest frameworks and tools for scalability and maintainability.'
      }
    ],
    technologies: [
      'React.js',
      'Next.js',
      'Vue.js',
      'Node.js',
      'Firebase',
      'Supabase',
      'MongoDB',
      'Java'
    ],
    process: [
      {
        title: 'Discovery',
        description: 'Understanding your business needs and project requirements'
      },
      {
        title: 'Planning',
        description: 'Creating detailed project roadmap and technical specifications'
      },
      {
        title: 'Design',
        description: 'Crafting beautiful and intuitive user interfaces'
      },
      {
        title: 'Development',
        description: 'Building your application with clean, efficient code'
      },
      {
        title: 'Testing',
        description: 'Rigorous quality assurance and performance testing'
      },
      {
        title: 'Deployment',
        description: 'Launching your application and providing ongoing support'
      }
    ],
    benefits: [
      'Increased online presence',
      'Improved user engagement',
      'Higher conversion rates',
      'Better brand recognition',
      'Scalable solutions',
      'Competitive advantage'
    ]
  },
  {
    id: 'ui-ux-design',
    icon: 'Palette',
    title: 'UI/UX Design',
    description: 'Beautiful, intuitive interfaces that enhance user experience and engagement.',
    color: 'from-purple-500 to-pink-500',
    bgColor: 'bg-gradient-to-br from-purple-100 to-pink-100',
    hoverBgColor: 'bg-gradient-to-br from-purple-200 to-pink-200',
    heroImage: 'https://images.unsplash.com/photo-1561070791-2526d30994b5?q=80&w=2000&auto=format&fit=crop',
    features: [
      {
        title: 'User Research',
        description: 'Understanding your users\' needs, behaviors, and pain points.'
      },
      {
        title: 'Interface Design',
        description: 'Creating visually appealing and functional user interfaces.'
      },
      {
        title: 'Interaction Design',
        description: 'Designing smooth and intuitive user interactions.'
      },
      {
        title: 'Prototyping',
        description: 'Building interactive prototypes for testing and validation.'
      }
    ],
    technologies: [
      'Figma',
      'Adobe XD',
      'Sketch',
      'React.js',
      'Vue.js',
      'Flutter',
      'Prototyping',
      'Wireframing'
    ],
    process: [
      {
        title: 'Research',
        description: 'Gathering insights about users and business requirements'
      },
      {
        title: 'Wireframing',
        description: 'Creating low-fidelity layouts and user flows'
      },
      {
        title: 'Visual Design',
        description: 'Developing the visual language and UI components'
      },
      {
        title: 'Prototyping',
        description: 'Building interactive prototypes for testing'
      },
      {
        title: 'Testing',
        description: 'Conducting user testing and gathering feedback'
      },
      {
        title: 'Implementation',
        description: 'Working with developers to ensure pixel-perfect execution'
      }
    ],
    benefits: [
      'Enhanced user satisfaction',
      'Reduced development costs',
      'Increased user retention',
      'Better conversion rates',
      'Stronger brand identity',
      'Competitive advantage'
    ]
  },
  {
    id: 'app-development',
    icon: 'Rocket',
    title: 'Mobile App Development',
    description: 'Native and cross-platform mobile applications for iOS and Android.',
    color: 'from-red-500 to-rose-500',
    bgColor: 'bg-gradient-to-br from-red-100 to-rose-100',
    hoverBgColor: 'bg-gradient-to-br from-red-200 to-rose-200',
    heroImage: 'https://images.unsplash.com/photo-1512941937669-90a1b58e7e9c?q=80&w=2070&auto=format&fit=crop',
    features: [
      {
        title: 'Native Development',
        description: 'High-performance native apps for iOS and Android platforms.'
      },
      {
        title: 'Cross-Platform Solutions',
        description: 'Cost-effective apps that work seamlessly across multiple platforms.'
      },
      {
        title: 'App Store Optimization',
        description: 'Strategic optimization for better visibility in app stores.'
      },
      {
        title: 'Offline Functionality',
        description: 'Apps that work reliably even without internet connection.'
      }
    ],
    technologies: [
      'React Native',
      'Flutter',
      'Firebase',
      'Supabase',
      'Node.js',
      'MongoDB',
      'Java',
      'Swift'
    ],
    process: [
      {
        title: 'Strategy',
        description: 'Defining app goals, target audience, and technical approach'
      },
      {
        title: 'Design',
        description: 'Creating intuitive and engaging mobile interfaces'
      },
      {
        title: 'Development',
        description: 'Building robust and scalable mobile applications'
      },
      {
        title: 'Testing',
        description: 'Comprehensive testing across devices and platforms'
      },
      {
        title: 'Deployment',
        description: 'Publishing to app stores and managing releases'
      },
      {
        title: 'Maintenance',
        description: 'Ongoing support, updates, and performance optimization'
      }
    ],
    benefits: [
      'Expanded market reach',
      'Increased customer engagement',
      'Enhanced brand presence',
      'Improved user experience',
      'Better customer retention',
      'Revenue growth opportunities'
    ]
  },
  {
    id: 'digital-marketing',
    icon: 'LineChart',
    title: 'Digital Marketing',
    description: 'Data-driven strategies to grow your online presence and reach.',
    color: 'from-green-500 to-emerald-500',
    bgColor: 'bg-gradient-to-br from-green-100 to-emerald-100',
    hoverBgColor: 'bg-gradient-to-br from-green-200 to-emerald-200',
    heroImage: 'https://images.unsplash.com/photo-1460925895917-afdab827c52f?q=80&w=2015&auto=format&fit=crop',
    features: [
      {
        title: 'SEO Optimization',
        description: 'Improving search engine rankings and organic traffic.'
      },
      {
        title: 'Content Marketing',
        description: 'Creating valuable content that attracts and engages your audience.'
      },
      {
        title: 'Email Marketing',
        description: 'Building and nurturing customer relationships through email.'
      },
      {
        title: 'Analytics & Reporting',
        description: 'Data-driven insights to optimize marketing performance.'
      }
    ],
    technologies: [
      'Google Analytics',
      'Google Ads',
      'Facebook Ads',
      'SEMrush',
      'Mailchimp',
      'HubSpot',
      'WordPress',
      'Ahrefs'
    ],
    process: [
      {
        title: 'Analysis',
        description: 'Evaluating current performance and market opportunities'
      },
      {
        title: 'Strategy',
        description: 'Developing comprehensive marketing plans'
      },
      {
        title: 'Implementation',
        description: 'Executing campaigns across multiple channels'
      },
      {
        title: 'Monitoring',
        description: 'Tracking performance and gathering data'
      },
      {
        title: 'Optimization',
        description: 'Refining strategies based on performance data'
      },
      {
        title: 'Reporting',
        description: 'Providing detailed insights and recommendations'
      }
    ],
    benefits: [
      'Increased website traffic',
      'Better lead generation',
      'Higher conversion rates',
      'Improved ROI',
      'Market insights',
      'Competitive advantage'
    ]
  },
  {
    id: 'social-media-marketing',
    icon: 'Share',
    title: 'Social Media Marketing',
    description: 'Strategic social media management to build brand awareness and engagement.',
    color: 'from-pink-500 to-rose-500',
    bgColor: 'bg-gradient-to-br from-pink-100 to-rose-100',
    hoverBgColor: 'bg-gradient-to-br from-pink-200 to-rose-200',
    heroImage: 'https://images.unsplash.com/photo-1611162617474-5b21e879e113?q=80&w=2074&auto=format&fit=crop',
    features: [
      {
        title: 'Content Strategy',
        description: 'Creating engaging content that resonates with your audience.'
      },
      {
        title: 'Community Management',
        description: 'Building and engaging with your social media community.'
      },
      {
        title: 'Paid Advertising',
        description: 'Targeted social media advertising campaigns.'
      },
      {
        title: 'Performance Analysis',
        description: 'Tracking and optimizing social media performance.'
      }
    ],
    technologies: [
      'Buffer',
      'Hootsuite',
      'Canva',
      'Adobe Creative Suite',
      'Facebook Business',
      'Instagram Creator',
      'Twitter Analytics',
      'LinkedIn Ads'
    ],
    process: [
      {
        title: 'Audit',
        description: 'Analyzing current social media presence and performance'
      },
      {
        title: 'Strategy',
        description: 'Developing platform-specific content strategies'
      },
      {
        title: 'Content Creation',
        description: 'Producing engaging social media content'
      },
      {
        title: 'Community Building',
        description: 'Growing and engaging with your audience'
      },
      {
        title: 'Advertising',
        description: 'Running targeted paid social campaigns'
      },
      {
        title: 'Analysis',
        description: 'Measuring and reporting on performance'
      }
    ],
    benefits: [
      'Increased brand awareness',
      'Better audience engagement',
      'Improved customer loyalty',
      'Higher website traffic',
      'Enhanced brand reputation',
      'Direct customer feedback'
    ]
  },
  {
    id: 'seo',
    icon: 'LineChart',
    title: 'SEO',
    description: 'Search Engine Optimization to improve your website\'s visibility and organic traffic.',
    color: 'from-indigo-500 to-blue-500',
    bgColor: 'bg-gradient-to-br from-indigo-100 to-blue-100',
    hoverBgColor: 'bg-gradient-to-br from-indigo-200 to-blue-200',
    heroImage: 'https://images.unsplash.com/photo-1432888622747-4eb9a8efeb07?q=80&w=2074&auto=format&fit=crop',
    features: [
      {
        title: 'Keyword Research',
        description: 'Identifying high-value keywords to target for maximum impact.'
      },
      {
        title: 'On-Page Optimization',
        description: 'Optimizing website content, structure, and meta elements.'
      },
      {
        title: 'Technical SEO',
        description: 'Improving website performance, crawlability, and indexability.'
      },
      {
        title: 'Link Building',
        description: 'Building high-quality backlinks to boost domain authority.'
      }
    ],
    technologies: [
      'Google Search Console',
      'SEMrush',
      'Ahrefs',
      'Moz',
      'Screaming Frog',
      'Google Analytics',
      'PageSpeed Insights',
      'Yoast SEO'
    ],
    process: [
      {
        title: 'Audit',
        description: 'Comprehensive analysis of current SEO performance'
      },
      {
        title: 'Research',
        description: 'Keyword research and competitor analysis'
      },
      {
        title: 'Strategy',
        description: 'Developing customized SEO strategy'
      },
      {
        title: 'Implementation',
        description: 'Executing on-page and technical optimizations'
      },
      {
        title: 'Content',
        description: 'Creating SEO-optimized content'
      },
      {
        title: 'Monitoring',
        description: 'Tracking rankings and performance metrics'
      }
    ],
    benefits: [
      'Higher search rankings',
      'Increased organic traffic',
      'Better user experience',
      'Higher conversion rates',
      'Long-term ROI',
      'Competitive advantage'
    ]
  },
  {
    id: 'software-development',
    icon: 'Code2',
    title: 'Software Development',
    description: 'Custom software solutions tailored to your business needs and requirements.',
    color: 'from-violet-500 to-purple-500',
    bgColor: 'bg-gradient-to-br from-violet-100 to-purple-100',
    hoverBgColor: 'bg-gradient-to-br from-violet-200 to-purple-200',
    heroImage: 'https://images.unsplash.com/photo-1555066931-4365d14bab8c?q=80&w=2070&auto=format&fit=crop',
    features: [
      {
        title: 'Custom Solutions',
        description: 'Tailored software applications built for your specific requirements.'
      },
      {
        title: 'Enterprise Applications',
        description: 'Scalable enterprise-grade software solutions.'
      },
      {
        title: 'API Development',
        description: 'Robust APIs for seamless system integration.'
      },
      {
        title: 'Legacy Modernization',
        description: 'Updating and modernizing existing software systems.'
      }
    ],
    technologies: [
      'React.js',
      'React Native',
      'Next.js',
      'Vue.js',
      'Flutter',
      'Firebase',
      'Supabase',
      'Node.js',
      'MongoDB',
      'Java'
    ],
    process: [
      {
        title: 'Requirements',
        description: 'Gathering and analyzing business requirements'
      },
      {
        title: 'Architecture',
        description: 'Designing scalable software architecture'
      },
      {
        title: 'Development',
        description: 'Building robust and maintainable code'
      },
      {
        title: 'Testing',
        description: 'Comprehensive testing and quality assurance'
      },
      {
        title: 'Deployment',
        description: 'Deploying to production environments'
      },
      {
        title: 'Support',
        description: 'Ongoing maintenance and support'
      }
    ],
    benefits: [
      'Improved efficiency',
      'Reduced operational costs',
      'Better data management',
      'Scalable solutions',
      'Competitive advantage',
      'Process automation'
    ]
  }
];