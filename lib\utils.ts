import { clsx, type ClassValue } from "clsx"
import { twMerge } from "tailwind-merge"

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

export function getAppwriteImageUrl(fileId: string): string {
  if (!fileId || !process.env.NEXT_PUBLIC_APPWRITE_API_ENDPOINT || !process.env.NEXT_PUBLIC_APPWRITE_PROJECT_ID || !process.env.NEXT_PUBLIC_APPWRITE_BLOG_IMAGES_BUCKET_ID) {
    return '';
  }

  try {
    return `${process.env.NEXT_PUBLIC_APPWRITE_API_ENDPOINT}/storage/buckets/${process.env.NEXT_PUBLIC_APPWRITE_BLOG_IMAGES_BUCKET_ID}/files/${fileId}/view?project=${process.env.NEXT_PUBLIC_APPWRITE_PROJECT_ID}`;
  } catch (error) {
    console.error('Error constructing image URL:', error);
    return '';
  }
}

/**
 * Generate a URL-safe slug from a title
 * @param title - The blog post title
 * @returns A URL-safe slug
 */
export function generateSlug(title: string): string {
  if (!title) return '';

  return title
    .toLowerCase()
    .trim()
    // Replace spaces with hyphens
    .replace(/\s+/g, '-')
    // Remove special characters except hyphens
    .replace(/[^a-z0-9-]/g, '')
    // Remove multiple consecutive hyphens
    .replace(/-+/g, '-')
    // Remove leading and trailing hyphens
    .replace(/^-+|-+$/g, '');
}

/**
 * Check if a string looks like a database ID (Appwrite format)
 * @param str - The string to check
 * @returns True if it looks like a database ID
 */
export function isValidDatabaseId(str: string): boolean {
  // Appwrite IDs are typically 20 characters long and contain alphanumeric characters
  return /^[a-zA-Z0-9]{20}$/.test(str);
}

/**
 * Generate a unique slug by appending a number if needed
 * @param baseSlug - The base slug
 * @param existingSlugs - Array of existing slugs to check against
 * @returns A unique slug
 */
export function generateUniqueSlug(baseSlug: string, existingSlugs: string[]): string {
  let slug = baseSlug;
  let counter = 1;

  while (existingSlugs.includes(slug)) {
    slug = `${baseSlug}-${counter}`;
    counter++;
  }

  return slug;
}